import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Request,
} from '@nestjs/common';
import { AnalyticsMediaService } from './analytics-media.service';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { readOrganizationWorkspaceAdAccountReports } from '../analytics.permissions';
import { PlatformMediaRequestDto } from './dto/platform-media-request.dto';

@Controller('analytics/organization/:organizationId')
export class AnalyticsMediaController {
  constructor(private readonly analyticsMediaService: AnalyticsMediaService) {}

  @Get('/media/:mediaId')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  getMediaVariant(
    @Request() request: any,
    @Param('mediaId', ParseIntPipe) mediaId: number,
  ) {
    const { userId } = request;

    return this.analyticsMediaService.getMediaForUser(mediaId, userId);
  }

  @Post('platform-media')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getPlatformMedia(
    @Request() request: any,
    @Param('organizationId') organizationId: string,
    @Body() platformMediaRequestDto: PlatformMediaRequestDto,
  ) {
    const { userId, headers } = request;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    return await this.analyticsMediaService.getPlatformMedia(
      userDetails,
      platformMediaRequestDto,
    );
  }
}
