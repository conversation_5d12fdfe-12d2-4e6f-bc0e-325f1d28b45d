import {
  Api<PERSON><PERSON>,
  ApiOperation,
  ApiParam,
  ApiQ<PERSON>y,
  ApiTags,
} from '@nestjs/swagger';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UsePipes,
  ValidationPipe,
  Version,
} from '@nestjs/common';
import { AnalyticsReportsService } from './analytics-reports.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import {
  SortOrderValidationPipe,
  StrictSortOrderValidationPipe,
} from '../../pagination-validators/sort-order-validation-pipe';
import {
  SortByValidationPipe,
  StrictSortByValidationPipe,
} from '../../pagination-validators/sort-by-validation-pipe';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { GetAllAnalyticsReportTypesValidationPipe } from './validators/analytics-report-types-validation-pipe';
import {
  AnalyticsReportType,
  VISIBLE_ANALYTICS_REPORT_TYPES,
} from './model/analytics-report';
import {
  createOrganizationWorkspaceAdAccountReports,
  readOrganizationAdAccountsDetails,
  readOrganizationWorkspaceAdAccountReports,
} from '../analytics.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  CreateAnalyticsReportDto,
  CreateCreativeLeaderboardReportDto,
  RenameAnalyticsReportDto,
} from './dto/create-analytics-report.dto';
import {
  AnalyticsReportDto,
  AnalyticsReportWithNoFiltersDto,
  LeaderboardReportDto,
} from './dto/analytics-report.dto';
import {
  FilterItem,
  GetAnalyticsReportFilterOptionsRequestDto,
} from './dto/get-analytics-filter-values.dto';
import { GetAnalyticsReportFilterOptionsValidationPipe } from './validators/get-analytics-report-filter-options-validation-pipe';
import { CreateAnalyticsReportValidationPipe } from './validators/create-analytics-report-validation-pipe';
import {
  FilterAdAccountRequestDto,
  ScopeFilterRequestDto,
} from '@vidmob/vidmob-organization-service-sdk';
import { buildCreateReportDto } from './util/create-report-factory';
import { RenameAnalyticsReportValidationPipe } from './validators/rename-analytics-report-validation-pipe';
import {
  GetAnalyticsReportBodyDto,
  GetAnalyticsReportFilterOptionsDto,
} from './dto/get-analytics-report-options.dto';
import { SortBy } from '../../reports/reports-enums';
import { ScoringReportType } from '../../reports/model/report';
import { SortOrder } from '../../reports/model/sort-order';
import { ReportListService } from './services/report-list-service';

@ApiTags('Report')
@Controller({
  path: 'analytics-report/organization/:organizationId',
})
export class AnalyticsReportsController {
  private readonly logger = new Logger(AnalyticsReportsController.name);

  constructor(
    private readonly analyticsReportService: AnalyticsReportsService,
    private readonly reportListService: ReportListService,
  ) {}

  @ApiParam({
    name: 'organizationId',
    description: 'The organization we get user saved reports for',
    required: true,
  })
  @ApiQuery({
    name: 'types',
    description: 'An array of report types to filter by.',
    required: false,
    example: `?types=${AnalyticsReportType.ELEMENT_IMPACT},${AnalyticsReportType.MEDIA_IMPACT}, ${AnalyticsReportType.ELEMENT_PRESENCE}`,
  })
  @ApiQuery({
    name: 'offset',
    description: 'The query offset',
    required: false,
  })
  @ApiQuery({
    name: 'perPage',
    description: 'The number of results',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The order of the sorting ASC / DESC',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'The field to sort the results by',
    required: false,
  })
  @ApiQuery({
    name: 'searchTerm',
    description:
      'Filtering results by search term in report name or description',
    required: false,
  })
  @VmApiOkPaginatedArrayResponse({
    description: 'Analytics report and metadata',
    type: AnalyticsReportDto,
  })
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Get('')
  getAllAnalyticsReports(
    @Request() req: any,
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('organizationId') organizationId: string,
    @Query('types', GetAllAnalyticsReportTypesValidationPipe)
    types: AnalyticsReportType[],
    @Query('sortOrder', SortOrderValidationPipe) sortOrder: SortOrder,
    @Query('sortBy', SortByValidationPipe) sortBy = SortBy.DateCreated,
    @Query('searchTerm') searchTerm: string | undefined,
  ): Promise<PaginatedResultArray<AnalyticsReportWithNoFiltersDto>> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };
    const getAnalyticsReportsOptions = {
      ...paginationOptions,
      sortOrder,
      sortBy,
      types,
      searchTerm,
    };

    return this.analyticsReportService.getAllAnalyticsReports(
      userDetails,
      getAnalyticsReportsOptions,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The organization we get user saved reports for',
    required: true,
  })
  @ApiQuery({
    name: 'offset',
    description: 'The query offset',
    required: false,
  })
  @ApiQuery({
    name: 'perPage',
    description: 'The number of results',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The order of the sorting ASC / DESC',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'The field to sort the results by',
    required: false,
  })
  @ApiBody({
    type: GetAnalyticsReportBodyDto,
  })
  @VmApiOkPaginatedArrayResponse({
    description: 'Analytics report and metadata',
    type: AnalyticsReportDto,
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Version('2')
  @Post('')
  @ApiOperation({
    summary:
      'Get user analytics report list by parsing the JSON in report_filter',
  })
  getAllAnalyticsReportsV2(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() body: GetAnalyticsReportBodyDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('sortOrder', StrictSortOrderValidationPipe)
    sortOrder: SortOrder = SortOrder.DESC,
    @Query('sortBy', StrictSortByValidationPipe) sortBy = SortBy.DateCreated,
  ): Promise<PaginatedResultArray<AnalyticsReportWithNoFiltersDto>> {
    const { userId, headers } = req;
    if (!body.types) {
      body.types = VISIBLE_ANALYTICS_REPORT_TYPES;
    }

    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    const getAnalyticsReportsOptions: GetAnalyticsReportFilterOptionsDto = {
      ...paginationOptions,
      sortOrder,
      sortBy,
      ...body,
    };

    return this.analyticsReportService.getAllAnalyticsReports(
      userDetails,
      getAnalyticsReportsOptions,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The organization we get user saved reports for',
    required: true,
  })
  @ApiQuery({
    name: 'offset',
    description: 'The query offset',
    required: false,
  })
  @ApiQuery({
    name: 'perPage',
    description: 'The number of results',
    required: false,
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The order of the sorting ASC / DESC',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'The field to sort the results by',
    required: false,
  })
  @ApiBody({
    type: GetAnalyticsReportBodyDto,
  })
  @VmApiOkPaginatedArrayResponse({
    description: 'Analytics report and metadata',
    type: AnalyticsReportDto,
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Version('3')
  @Post()
  @ApiOperation({
    summary:
      'Get user analytics report list by leveraging mapping tables instead of report_filter',
  })
  getAllAnalyticsReportsV3(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() body: GetAnalyticsReportBodyDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('sortOrder', StrictSortOrderValidationPipe)
    sortOrder: SortOrder = SortOrder.DESC,
    @Query('sortBy', StrictSortByValidationPipe) sortBy = SortBy.DateCreated,
  ): Promise<PaginatedResultArray<AnalyticsReportWithNoFiltersDto>> {
    const { userId, headers } = req;
    if (!body.types) {
      body.types = VISIBLE_ANALYTICS_REPORT_TYPES;
    }

    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    const getAnalyticsReportsOptions: GetAnalyticsReportFilterOptionsDto = {
      ...paginationOptions,
      sortOrder,
      sortBy,
      ...body,
    };

    return this.reportListService.getAllAnalyticsReports(
      userDetails,
      getAnalyticsReportsOptions,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: "The id of organization user's report is created for",
    required: true,
  })
  @Post('')
  @Permissions(createOrganizationWorkspaceAdAccountReports)
  @UsePipes(new ValidationPipe({ transform: true }))
  async createAnalyticsReport(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(CreateAnalyticsReportValidationPipe)
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
  ): Promise<AnalyticsReportDto> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    return this.analyticsReportService.saveAnalyticsReport(
      userDetails,
      createAnalyticsReportDto,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: "The id of organization user's report is created for",
    required: true,
  })
  @ApiParam({
    name: 'reportId',
    description: 'The report to duplicate',
    required: true,
  })
  @Post('report/:reportId')
  @Permissions(createOrganizationWorkspaceAdAccountReports)
  @UsePipes(new ValidationPipe({ transform: true }))
  async duplicateAnalyticsReport(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
  ) {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    const reportToDuplicate: AnalyticsReportDto | LeaderboardReportDto =
      await this.analyticsReportService.getAnalyticsReportForUser(
        userDetails,
        reportId,
      );

    const createReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto
      | undefined = buildCreateReportDto(reportToDuplicate, true);

    if (createReportDto) {
      return this.analyticsReportService.saveAnalyticsReport(
        userDetails,
        createReportDto,
      );
    } else {
      const errorMessage = `Error duplicating report ${reportId}, cannot find report to duplicate`;
      this.logger.error(errorMessage);
      throw new BadRequestException(errorMessage);
    }
  }

  @ApiParam({
    name: 'organizationId',
    description: "The id of organization user's report is created for",
    required: true,
  })
  @ApiParam({
    name: 'reportId',
    description: 'The report to duplicate',
    required: true,
  })
  @Patch('report/:reportId/rename')
  @Permissions(createOrganizationWorkspaceAdAccountReports)
  @UsePipes(new ValidationPipe({ transform: true }))
  async renameAnalyticsReport(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
    @Body(RenameAnalyticsReportValidationPipe)
    renameAnalyticsReportDto: RenameAnalyticsReportDto,
  ): Promise<AnalyticsReportDto> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    const reportToRename: AnalyticsReportDto | LeaderboardReportDto =
      await this.analyticsReportService.getAnalyticsReportForUser(
        userDetails,
        reportId,
      );

    const createReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto
      | undefined = buildCreateReportDto(reportToRename, false);

    if (!createReportDto) {
      const errorMessage = `Error renaming report ${reportId}, cannot find report to rename`;
      this.logger.error(errorMessage);
      throw new BadRequestException(errorMessage);
    }

    if (reportToRename?.createdBy?.id !== userId) {
      const errorMessage = `Error renaming report ${reportId}, user ${userId} is not the creator of the report`;
      this.logger.error(errorMessage);
      throw new BadRequestException(errorMessage);
    }

    const renamedCreateReportDto = {
      ...createReportDto,
      ...(renameAnalyticsReportDto.name && {
        name: renameAnalyticsReportDto.name,
      }),
      ...((renameAnalyticsReportDto.description ||
        renameAnalyticsReportDto.description === '') && {
        description: renameAnalyticsReportDto.description,
      }),
    };

    return this.analyticsReportService.updateAnalyticsReport(
      userDetails,
      reportId,
      renamedCreateReportDto,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: "The id of organization user's report was created for",
    required: true,
  })
  @ApiParam({
    name: 'reportId',
    description: "The id of user's report to update",
    required: true,
  })
  @Patch('report/:reportId')
  @Permissions(createOrganizationWorkspaceAdAccountReports)
  async updateAnalyticsReport(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
    @Body(CreateAnalyticsReportValidationPipe)
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
  ): Promise<AnalyticsReportDto> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    return this.analyticsReportService.updateAnalyticsReport(
      userDetails,
      reportId,
      createAnalyticsReportDto,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: "The organization user's report was created for",
    required: true,
  })
  @ApiParam({
    name: 'reportId',
    description: 'The id of report to fetch for user',
    required: true,
  })
  @Get('report/:reportId')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getSingleAnalyticsReport(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
  ): Promise<AnalyticsReportDto> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    return this.analyticsReportService.getAnalyticsReportForUser(
      userDetails,
      reportId,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The organization user report was created for',
    required: true,
  })
  @ApiParam({
    name: 'reportId',
    description: 'The id of report to delete',
    required: true,
  })
  @Delete('report/:reportId')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async deleteAnalyticsReport(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
  ): Promise<{ deleted: boolean }> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    return this.analyticsReportService.deleteAnalyticsReportForUser(
      userDetails,
      reportId,
    );
  }

  /**
   * @deprecated Use v2 of this endpoint
   * @param req
   * @param organizationId
   * @param getAnalyticsReportFilterOptionsRequestDto
   * @param paginationOptions
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The organization user report was created for',
    required: true,
  })
  @Permissions(readOrganizationAdAccountsDetails)
  @Post('filters')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getAnalyticsReportFilterOptions(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(GetAnalyticsReportFilterOptionsValidationPipe)
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<FilterItem[]> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    return this.analyticsReportService.getAnalyticsReportFilterOptions(
      userDetails,
      getAnalyticsReportFilterOptionsRequestDto,
      paginationOptions,
    );
  }

  /**
   * Get filter values for analytics data.
   * Implements pagination and string search for identifier filters
   *
   * @param req
   * @param organizationId
   * @param getAnalyticsReportFilterOptionsRequestDto
   * @param paginationOptions
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The organization user report was created for',
    required: true,
  })
  @Permissions(readOrganizationAdAccountsDetails)
  @Version('2')
  @Post('filters')
  @UsePipes(new ValidationPipe({ transform: true }))
  async getAnalyticsReportFilterOptionsV2(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(GetAnalyticsReportFilterOptionsValidationPipe)
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<FilterItem> | FilterItem[]> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    return this.analyticsReportService.getAnalyticsReportFilterOptionsV2(
      userDetails,
      getAnalyticsReportFilterOptionsRequestDto,
      paginationOptions,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The organization id for the report',
    required: true,
  })
  @Post('filtered-ad-accounts')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getFilteredAdAccountsForBrandsOrMarkets(
    @Body() filterAdAccountRequestDto: FilterAdAccountRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.analyticsReportService.filterAdAccountsForBrandsOrMarketBreakdown(
      filterAdAccountRequestDto,
      paginationOptions,
    );
  }

  @VmApiOkResponse({
    description: 'Scope filters successfully retrieved',
  })
  @Post('/scopeFilter')
  @Permissions(createOrganizationWorkspaceAdAccountReports)
  async filterAdAccountsByScope(
    @Param('organizationId') organizationId: string,
    @Body() dto: ScopeFilterRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return await this.analyticsReportService.filterAdAccountsByScope(
      dto,
      paginationOptions,
    );
  }

  @Get('reports/users')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getUsersWithReportsByWorkspacesIds(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Query('workspaceIds') workspaceIds: string,
    @Query('reportTypes')
    reportTypes: string,
  ) {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    const workspaceIdsArray = workspaceIds.split(',').map(Number);

    const reportTypesArray = reportTypes
      ?.split(',')
      .map((type) => type.trim()) as
      | AnalyticsReportType[]
      | ScoringReportType[];

    return this.analyticsReportService.getAllUsersThatHaveReportsInUserWorkspaces(
      userDetails,
      workspaceIdsArray,
      reportTypesArray,
    );
  }
}
