import {
  BadRequestException,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  Request,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { InsightPermissionService } from './insight-permission.service';
import { UserDetailsDto } from '../../dto/user-details.dto';

@ApiTags('Insight Permission')
@Controller(
  'insight-permission/organization/:organizationId/workspace/:workspaceId',
)
export class InsightPermissionController {
  constructor(
    private readonly insightPermissionService: InsightPermissionService,
  ) {}

  /**
   * Fetches the user's access permissions for a specific insight or folder.
   * @param organizationId
   * @param workspaceId
   * @param insightId
   * @param folderId
   * @param req
   */
  @Get()
  async getSharedInsightPermissions(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Request() req: any,
    @Query('insightId') insightId?: string,
    @Query('folderId') folderId?: string,
  ) {
    if (insightId && folderId) {
      throw new BadRequestException(
        'Cannot provide both insightId and folderId at the same time.',
      );
    }

    const userDetails: UserDetailsDto = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId,
    };

    const [hasAccessToWorkspace, hasAccessToOrganization] = await Promise.all([
      this.insightPermissionService.getWorkspaceAccessPermission(
        userDetails,
        workspaceId,
      ),
      this.insightPermissionService.getOrganizationAccessPermission(
        userDetails,
        organizationId,
      ),
    ]);

    const result: Record<string, boolean> = {
      hasAccessToWorkspace,
      hasAccessToOrganization,
    };

    if (insightId) {
      result.hasAccessToInsight =
        await this.insightPermissionService.getInsightAccessPermission(
          userDetails,
          insightId,
        );
    } else if (folderId) {
      result.hasAccessToFolder =
        await this.insightPermissionService.getFolderAccessPermission(folderId);
    }

    return result;
  }
}
