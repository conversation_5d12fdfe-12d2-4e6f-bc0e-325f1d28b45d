import { <PERSON>, Get, Param, ParseIntPipe } from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import { DraftMediaVariantService as MediaVariantService } from '../../draft-media-variant/services/draft-media-variant.service';
import { readProjectMedia } from '../../studio.permissions';
import { ReadMediaVariantDto } from '../dto/read-media-variant.dto';
import { VmApiOkResponse } from '@vidmob/vidmob-nestjs-common';

/**
 * * The controller responsible for listing out draft media variant.
 */
@ApiTags('MediaVariant')
@Controller('project/:projectId/media-variant')
export class MediaVariantController {
  constructor(private readonly mediaVariantService: MediaVariantService) {}
  /**
   * Find the media variant url, duration and status by media and project id.
   * @param mediaId The media id.
   * @param projectId The project id.
   */
  @VmApiOkResponse({
    type: ReadMediaVariantDto,
  })
  @ApiParam({ name: 'mediaId', description: 'The id of the Media' })
  @ApiParam({ name: 'projectId', description: 'The id of the Project' })
  @Permissions(readProjectMedia)
  @Get(':mediaId')
  async findMediaVariantUrl(
    @Param('mediaId', ParseIntPipe) mediaId: number,
    @Param('projectId', ParseIntPipe) projectId: number,
  ) {
    return this.mediaVariantService.findMediaVariantUrl(mediaId, projectId);
  }
}
