import {
  WidgetDataConfig,
  WidgetType,
  WidgetTypeDefinition,
  WorkspaceSubscription,
} from './dashboard-types';
import {
  ASSETS_SCORED_WIDGET,
  AssetsScoredDataSchema,
  assetsScoredTransforms,
} from './schemas/widget-types/assets-scored';
import { ASSET_OVERVIEW_WIDGET } from './schemas/widget-types/asset-overview';
import { KEY_FINDINGS_WIDGET } from './schemas/widget-types/key-findings';
import {
  CRITERIA_PERFORMANCE_WIDGET,
  CriteriaPerformanceDataSchema,
  criteriaPerformanceTransforms,
  enrichCriteriaPerformance,
  finalizeCriteriaPerformanceFilter,
} from './schemas/widget-types/criteria-performance';
import {
  ADHERENCE_SCORE_WIDGET,
  AdherenceScoreDataSchema,
  adherenceScoreTransforms,
} from './schemas/widget-types/adherence-score';
import { BadRequestException } from '@nestjs/common';

export const MAX_WIDGETS_PER_DASHBOARD = 16;

export const CURRENT_DASHBOARD_FILTER_VERSION = 2;

export const WIDGET_TYPE_DEFINITIONS: WidgetTypeDefinition[] = [
  ASSETS_SCORED_WIDGET,
  ASSET_OVERVIEW_WIDGET,
  KEY_FINDINGS_WIDGET,
  CRITERIA_PERFORMANCE_WIDGET,
  ADHERENCE_SCORE_WIDGET,
];

export const getWidgetTypeDefinition = (
  widgetType: WidgetType,
): WidgetTypeDefinition => {
  const def = WIDGET_TYPE_DEFINITIONS.find((w) => w.widgetType === widgetType);
  if (!def)
    throw new BadRequestException(`Widget type ${widgetType} not supported`);
  return def;
};

export const dashboardSubscriptionTypes = [
  WorkspaceSubscription.ANALYTICS,
  WorkspaceSubscription.SCORING,
];

export const widgetDataRegistry: Partial<Record<WidgetType, WidgetDataConfig>> =
  {
    [WidgetType.ASSETS_SCORED]: {
      schema: AssetsScoredDataSchema,
      transforms: assetsScoredTransforms,
    },
    [WidgetType.CRITERIA_PERFORMANCE]: {
      schema: CriteriaPerformanceDataSchema,
      transforms: criteriaPerformanceTransforms,
      finalizeFilter: finalizeCriteriaPerformanceFilter,
      enrich: enrichCriteriaPerformance,
    },
    [WidgetType.ADHERENCE_SCORE]: {
      schema: AdherenceScoreDataSchema,
      transforms: adherenceScoreTransforms,
    },
  } as const;
