import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  ParseUUIDPipe,
  Request,
  Query,
  Put,
  UsePipes,
  ValidationPipe,
  HttpCode,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { DashboardService } from './dashboard.service';
import { CreateDashboardDto } from '../dto/create-dashboard.dto';
import { UpdateDashboardDto } from '../dto/update-dashboard.dto';
import { ReadDashboardDto } from '../dto/read-dashboard.dto';
import {
  anyOrganizationRole,
  readOrganizationDashboard,
} from '../../analytics/analytics.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import {
  MultiSortByPipe,
  MultiSortOrderPipe,
} from '../../pagination-validators/sort-order-validation-pipe';
import { UserDetailsDto } from '../../analytics/dto/user-details.dto';
import { DashboardSortBy, SortOrder } from '../constants/constants';
import {
  GetDashboardListFilterQueryDto,
  ListDashboardsDto,
} from '../dto/list-dashboards.dto';
import { UpdateDashboardFavoriteDto } from '../dto/update-dashboard-favorite.dto';

@Permissions(readOrganizationDashboard)
@ApiTags('Executive Dashboard - Dashboards')
@Controller('executive-dashboard/organization/:organizationId/dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Post()
  @VmApiOkResponse({ type: ReadDashboardDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async create(
    @Request() request: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Body() createDashboardDto: CreateDashboardDto,
  ): Promise<ReadDashboardDto> {
    const userDetails = {
      organizationId,
      userId: request.userId,
    };
    return await this.dashboardService.create(userDetails, createDashboardDto);
  }

  @Post('list')
  @VmApiOkPaginatedArrayResponse({ type: ReadDashboardDto })
  async listDashboards(
    @Request() request: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Query() paginationOptions: PaginationOptions,
    @Query('sortBy', new MultiSortByPipe())
    sortBy: DashboardSortBy[] = [DashboardSortBy.LAST_UPDATED],
    @Query('sortOrder', new MultiSortOrderPipe())
    sortOrder: SortOrder[] = [SortOrder.DESC],
    @Body() listDashboardsRequestBody: ListDashboardsDto,
  ): Promise<PaginatedResultArray<ReadDashboardDto>> {
    const authorization = request.headers.authorization;
    const { userId } = request;

    const userDetails: UserDetailsDto = {
      organizationId,
      userId,
      authorization,
    };

    const getDashboardFilterOptions: GetDashboardListFilterQueryDto = {
      ...paginationOptions,
      sortOrder,
      sortBy,
      ...listDashboardsRequestBody,
    };

    return await this.dashboardService.listDashboards(
      userDetails,
      getDashboardFilterOptions,
    );
  }

  @Get(':dashboardId')
  @VmApiOkResponse({ type: ReadDashboardDto })
  async getDashboard(
    @Request() request: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('dashboardId', new ParseUUIDPipe()) dashboardId: string,
  ): Promise<ReadDashboardDto> {
    const { userId } = request;
    const authorization = request.headers.authorization;
    const userDetails: UserDetailsDto = {
      organizationId,
      userId,
      authorization,
    };
    return await this.dashboardService.getDashboard(userDetails, dashboardId);
  }

  @Patch(':dashboardId')
  @VmApiOkResponse({ type: ReadDashboardDto })
  @UsePipes(new ValidationPipe({ transform: true }))
  async update(
    @Request() req: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('dashboardId', new ParseUUIDPipe()) dashboardId: string,
    @Body() dto: UpdateDashboardDto,
  ): Promise<ReadDashboardDto> {
    const userDetails = {
      organizationId,
      userId: req.userId as number,
    };
    return await this.dashboardService.update(userDetails, dashboardId, dto);
  }

  @Delete(':dashboardId')
  @HttpCode(204)
  async remove(
    @Request() req: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('dashboardId', new ParseUUIDPipe()) dashboardId: string,
  ): Promise<void> {
    const userId = req.userId as number;
    return await this.dashboardService.remove(
      { organizationId, userId },
      dashboardId,
    );
  }

  @Put(':dashboardId/favorite')
  @UsePipes(new ValidationPipe({ transform: true }))
  @ApiOkResponse({ description: 'Favorite flag updated' })
  async updateFavorite(
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('dashboardId', new ParseUUIDPipe()) dashboardId: string,
    @Body() dto: UpdateDashboardFavoriteDto,
    @Request() req: any,
  ): Promise<{ id: string; isFavorite: boolean }> {
    const userId = req.userId as number;
    return await this.dashboardService.updateFavorite(
      userId,
      organizationId,
      dashboardId,
      dto.isFavorite,
    );
  }
}
