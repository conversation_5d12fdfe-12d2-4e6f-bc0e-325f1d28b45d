import { Controller, Get, VERSION_NEUTRAL } from '@nestjs/common';
import { Public } from '../auth/decorators/public.decorator';
import { ServiceStatusService } from './service-status.service';

@Public()
@Controller({
  path: 'service-status',
  version: VERSION_NEUTRAL,
})
export class ServiceStatusController {
  constructor(private readonly serviceStatusService: ServiceStatusService) {}

  @Get()
  getStatus() {
    return this.serviceStatusService.getStatus();
  }
}
