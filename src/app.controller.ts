import {
  Controller,
  Get,
  Logger,
  Param,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AppService } from './app.service';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { UserResponseDto } from './dto/user-response.dto';
import { Public } from './auth/decorators/public.decorator';
import {
  partnerFromQueryHandler,
  Permissions,
  projectFromQueryHandler,
} from './auth/decorators/permission.decorator';
import { PermissionAction } from './auth/enums/permission.action.enum';
import { PermissionSubResource } from './auth/enums/permission.subresource.enum';
import { PermissionDomain } from './auth/enums/permission.domain.enum';

@ApiTags('Public API')
@ApiSecurity('Bearer Token')
@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) {}

  /**
   * Decorator Public will become this endpoint public, for instance used for health check or login
   * @returns text
   */
  @Public()
  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  /**
   * This endpoint will check permission for a Workspace (commonly known as partner), which is passed in request but got by  domainContextHandler.
   * After getting the permission, the attributes from legacy will be represented by: type (action),
   * resource (@Permissions ({ domain: PermissionDomain.WORKSPACE, ...}) ) and subresource feature/data to be accessed.
   * @returns text
   */
  @Get('helloPartner')
  @Permissions({
    domain: PermissionDomain.WORKSPACE,
    domainContextHandler: partnerFromQueryHandler,
    required: [
      {
        action: PermissionAction.READ,
        subresource: PermissionSubResource.DETAILS,
      },
      {
        action: PermissionAction.CREATE,
        subresource: PermissionSubResource.USER_INVITE,
      },
    ],
  })
  getHelloPartner(): string {
    return this.appService.getHello() + ' Partner';
  }

  /**
   * This endpoint will check permission for a Workspace (commonly known as partner), which is passed in request but got by  domainContextHandler.
   * After getting the permission, the attributes from legacy will be represented by: type (action),
   * resource (@Permissions ({ domain: PermissionDomain.WORKSPACE, ...}) ) and subresource feature/data to be accessed.
   * @returns text
   */
  @Get('helloProject')
  @Permissions({
    domain: PermissionDomain.PROJECT,
    domainContextHandler: projectFromQueryHandler,
    required: [
      {
        action: PermissionAction.UPDATE,
        subresource: PermissionSubResource.PROJECT,
      },
    ],
  })
  getHelloProject(): string {
    return this.appService.getHello() + ' Project';
  }

  /**
   * Without @Public decorator all requests to this endpoint will be checked for permission based on Auth Bearer Token
   */
  @Public()
  @Get('user')
  async getUser(@Request() req: any): Promise<UserResponseDto> {
    const { userId, username, authorities } = req;
    this.logger.log(
      `Test called with user ${userId || 'anonymous'} ${
        username || 'anonymous'
      } authorities=${JSON.stringify(authorities || [])}`,
    );
    return {
      id: userId || 'anonymous',
      username: username || 'anonymous',
      authorities: authorities || [],
    };
  }
}
