import { AxiosError } from 'axios/index';
import { HttpException } from '@nestjs/common';

export const rethrowAxiosError = (err: any): never => {
  if (err.isAxiosError && err.response) {
    const axiosError: AxiosError = err;
    const { data, status } = axiosError.response!;
    if (data && typeof data === 'object' && 'status' in data) {
      throw new HttpException(data, status);
    }
  }
  throw err;
};
