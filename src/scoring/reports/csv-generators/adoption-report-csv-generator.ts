import { Logger } from '@nestjs/common';
import {
  JOIN_STRING,
  NOT_SPECIFIED_LABEL,
  NOT_SPECIFIED_VALUE,
} from '../constants/constants';
import {
  AdoptionReportDataItem,
  CsvLabel,
} from '../internal-scoring-reports.interfaces';
import { countryDataMapByIsoCode } from '../constants/country-data';
import { CreateReportDto } from '@vidmob/vidmob-soa-scoring-service-sdk';

export abstract class ScoringReportCsvGenerator<T> {
  private readonly logger = new Logger(ScoringReportCsvGenerator.name);

  /**
   * Sort countries alphabetically, with "Not Specified" at the end.
   * @param a - country name to compare
   * @param b - country name to compare
   */
  private sortCountries = (a: string, b: string) => {
    if (
      a.startsWith(NOT_SPECIFIED_LABEL) &&
      !b.startsWith(NOT_SPECIFIED_LABEL)
    ) {
      return 1; // Put a after b
    } else if (
      !a.startsWith(NOT_SPECIFIED_LABEL) &&
      b.startsWith(NOT_SPECIFIED_LABEL)
    ) {
      return -1; // Put a before b
    } else {
      return a.localeCompare(b); // Sort alphabetically
    }
  };

  /**
   * Criteria need special handling since they are made up of three components.
   * @private
   */
  protected getLabelForProp(cell: Record<string, any>, prop: string): CsvLabel {
    const value = cell[prop];
    return { uniqueName: value, displayName: value };
  }

  /**
   * Create a name for a cell based on the given sections. Transform NOT_SPECIFIED.
   */
  private uniqueLabelForCell = (
    cell: Record<string, string>,
    sections: (keyof typeof cell)[],
  ): string => {
    return sections
      .map((section) => {
        const value = this.getLabelForProp(cell, section).uniqueName;
        if (section === 'market' || section === 'brand') {
          if (value === NOT_SPECIFIED_VALUE) {
            return NOT_SPECIFIED_LABEL;
          }
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          const countryName = countryDataMapByIsoCode[value];
          if (countryName) {
            return countryName;
          }
        }
        return value;
      })
      .join(JOIN_STRING);
  };

  protected abstract getValues(): string[];

  /**
   * Generate a CSV from the given report.
   * @private
   */
  generateCsv(createReportDto: CreateReportDto, reportData: any[]) {
    const columns = createReportDto.groupBy.columns;
    const rows = createReportDto.groupBy.rows;

    //build a map of all values (unique and display) for each column
    const columnLabelValues: Record<string, CsvLabel[]> = {};

    for (const col of columns) {
      const foundValues = reportData.map((cell) =>
        this.getLabelForProp(cell, col),
      );

      const uniqueValues: Record<string, CsvLabel> = {};
      foundValues.forEach((label) => {
        uniqueValues[label.uniqueName] = label;
      });
      columnLabelValues[col] = Object.values(uniqueValues).sort((a, b) =>
        a.displayName.localeCompare(b.displayName),
      );
    }

    //join the found column label values into all combinations
    const flattenedColumns = this.flattenColumns(columnLabelValues, columns);
    const colPositionMap: Record<string, number> = {};
    flattenedColumns.forEach((col, index) => {
      colPositionMap[col.uniqueName] = index;
    });
    const rowLength = flattenedColumns.length;
    const rowData: Record<string, string[]> = {};
    reportData.forEach((cell) => {
      const rowName = this.uniqueLabelForCell(cell, rows);
      let row = rowData[rowName];
      if (!row) {
        row = this.createEmptyRow(rowLength);
        rowData[rowName] = row;
      }
      const cellDataParts = this.getValues();
      const cellDataValue = cellDataParts.map((v) => cell[v]).join('/');

      const columnName = this.uniqueLabelForCell(cell, columns);
      const columnIndex = colPositionMap[columnName];

      if (row[columnIndex] !== '') {
        this.logger.error(
          `Duplicate key processing row: ${rowName}, column ${columnName}`,
        );

        //a mismatch is unexpected and could go unnoticed, so throw an error
        throw new Error(`Duplicate key processing row: ${rowName}`);
      }
      const isFormula = cellDataParts.length > 1;
      row[columnIndex] = (isFormula ? '=' : '') + cellDataValue;
    });

    const rowTotals = this.calculateTotals(createReportDto, reportData);
    const csvTotalHeaders = ['Total | IN_FLIGHT', 'Total | PRE_FLIGHT'];

    const t = [...Object.keys(rowData)];

    return [
      rows.map((s) => this.upperCaseFirstLetter(s)).join(',') +
        ',' +
        csvTotalHeaders.map((x) => this.escapeCsvValue(x)).join(',') +
        ',' +
        flattenedColumns
          .map((x) => this.escapeCsvValue(x.displayName))
          .join(','),
      ...Object.keys(rowData)
        .sort(this.sortCountries)
        .map((key) =>
          [
            key.split(JOIN_STRING).map((x) => this.escapeCsvValue(x)),
            ...rowTotals[key],
            ...rowData[key],
          ].join(','),
        ),
    ].join('\n');
  }

  private calculateTotals(createReportDto: CreateReportDto, reportData: any[]) {
    const rows = createReportDto.groupBy.rows;

    const rowTotals: Record<string, number[][]> = {};

    reportData.forEach((cell) => {
      const rowName = this.uniqueLabelForCell(cell, rows);

      const row = rowTotals[rowName];
      if (!row) {
        rowTotals[rowName] = [
          [0, 0],
          [0, 0],
        ]; // first pair inFlight second pair preFlight
      }

      const cellDataParts = this.getValues();
      const cellDataValue = cellDataParts.map((v) => cell[v]).map((v) => +v);
      for (let i = 0; i < cellDataParts.length; i++) {
        if (cell.batchType === 'IN_FLIGHT') {
          const totalInFlight = rowTotals[rowName][0];
          totalInFlight[i] = totalInFlight[i] + cellDataValue[i];
        } else if (cell.batchType === 'PRE_FLIGHT') {
          const totalPreFlight = rowTotals[rowName][1];
          totalPreFlight[i] = totalPreFlight[i] + cellDataValue[i];
        }
      }
    });

    const rowTotalsString: Record<string, string[]> = {};
    for (const key in rowTotals) {
      if (rowTotals.hasOwnProperty(key)) {
        const numberArray = rowTotals[key];
        rowTotalsString[key] = numberArray.map((value) => {
          return value[1] === 0 ? `${value[0]}` : `=${value[0]}/${value[1]}`;
        });
      }
    }

    return rowTotalsString;
  }

  private upperCaseFirstLetter(str: string) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * Some (criteria parameter) values need to be escaped to be properly handled in a CSV.
   * @private
   */
  protected escapeCsvValue(value: string) {
    // If the value contains a comma, double quote, or newline character,
    // enclose it within double quotes and escape any double quotes inside.
    if (/[,"\n]/.test(value)) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }

  /**
   * Create an empty row of a given length.
   * @param length - corresponds to the number of columns in the report
   * @private
   */
  private createEmptyRow(length: number): string[] {
    return Array.from({ length }, () => '');
  }

  /**
   * Given the order of the columns and returned values, create all permutations of the column labels.
   * @param columnValues - all possible values for each column
   * @param columnTypes - the types of columns in the report
   * @private
   */
  private flattenColumns<T extends string>(
    columnValues: Record<T, CsvLabel[]>,
    columnTypes: T[],
  ): CsvLabel[] {
    if (columnTypes.length === 0) {
      return [];
    }
    const columnLabel: CsvLabel[] = [];
    for (const label of columnValues[columnTypes[0]]) {
      const subLabels = this.flattenColumns(columnValues, columnTypes.slice(1));
      for (const subLabel of subLabels) {
        columnLabel.push({
          displayName: `${label.displayName}${JOIN_STRING}${subLabel.displayName}`,
          uniqueName: `${label.uniqueName}${JOIN_STRING}${subLabel.uniqueName}`,
        });
      }
      if (subLabels.length === 0) {
        columnLabel.push({
          uniqueName: label.uniqueName,
          displayName: label.displayName,
        });
      }
    }
    return columnLabel;
  }
}

export class ScoringAdoptionReportCsvGenerator extends ScoringReportCsvGenerator<AdoptionReportDataItem> {
  protected getValues(): (keyof AdoptionReportDataItem)[] {
    return ['totalScored'];
  }
}
