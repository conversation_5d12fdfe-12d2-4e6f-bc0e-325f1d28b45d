import {
  AdherenceReportDataItem,
  CsvLabel,
} from '../internal-scoring-reports.interfaces';
import { getDisplayNameFor } from '../constants/criteria-names';
import { JOIN_STRING } from '../constants/constants';
import { ScoringReportCsvGenerator } from './adoption-report-csv-generator';

export class ScoringAdherenceReportCsvGenerator extends ScoringReportCsvGenerator<AdherenceReportDataItem> {
  protected getLabelForProp(
    cell: AdherenceReportDataItem,
    prop: keyof AdherenceReportDataItem,
  ): CsvLabel {
    if (prop === 'criteria') {
      //handle criteria column specially since it's made up of subcomponents
      const criteria = cell.criteria;
      const criteriaParameters = criteria.criteriaParameters;

      const displayName =
        ScoringAdherenceReportCsvGenerator.getCriteriaDisplayName(criteria);

      const criteriaPlatformLabel = criteria.criteriaPlatform
        ? criteria.criteriaPlatform
        : '';
      const criteriaParamLabel =
        criteriaParameters === '{}' ? '' : criteriaParameters + ' ';

      const uniqueName = `${criteria.criteriaName}${criteria.criteriaIdentifier}${criteriaParamLabel}${criteriaPlatformLabel}${criteria.criteriaIsOptional}`;
      return { displayName, uniqueName };
    }
    return super.getLabelForProp(cell, prop);
  }

  protected getValues(): (keyof AdherenceReportDataItem)[] {
    return ['assetsPassed', 'assetsScored'];
  }

  static getCriteriaDisplayName(criteria: AdherenceReportDataItem['criteria']) {
    const criteriaParameters = criteria.criteriaParameters;
    const criteriaPlatformLabel = criteria.criteriaPlatform
      ? criteria.criteriaPlatform
      : '';

    const legacyDisplayName = getDisplayNameFor(
      criteria.criteriaIdentifier,
      JSON.parse(criteriaParameters),
    );
    const displayName = criteria.criteriaName
      ? criteria.criteriaName
      : legacyDisplayName;

    return (
      criteriaPlatformLabel +
      JOIN_STRING +
      displayName +
      (criteria.criteriaIsOptional ? ' (Optional)' : '')
    );
  }
}
