import { Controller, Get } from '@nestjs/common';
import { ScoreService } from './score.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Scores')
@Controller('score')
export class ScoringNormsController {
  constructor(private readonly scoreService: ScoreService) {}

  /**
   * return a scoring norms scopes.
   */
  @Get('norms/scopes')
  async getScoringNormsScopes() {
    const scopes = await this.scoreService.getScoringNormsScopes();
    return {
      scopes: scopes,
    };
  }

  @Get('norms/objectives')
  async getScoringNormsObjectives() {
    return this.scoreService.getScoringNormsObjectives();
  }
}
