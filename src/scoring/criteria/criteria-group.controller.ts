import { ApiQ<PERSON>y, ApiTags } from '@nestjs/swagger';
import {
  ForbiddenException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  Logger,
} from '@nestjs/common';
import { CriteriaGroupService } from './criteria-group.service';
import {
  readCriteriaGroup,
  createCriteriaGroup,
  updateCriteriaGroup,
  updateCriteriaGroupForWorkspace,
  deleteCriteriaGroup,
} from '../scoring.permissions';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { GetCriteriaGroups200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getCriteriaGroups200Response';
import { CreateCriteriaGroup } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/createCriteriaGroup';
import { ManageCriteriaGroupCriteriaRequest } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/manageCriteriaGroupCriteriaRequest';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { GetSingleCriteriaGroup200Response } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { OrganizationService as OrganizationServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/organization.service';

@ApiTags('Criteria Group')
@Controller('criteria-group/organization/:organizationId')
export class CriteriaGroupController {
  logger = new Logger(CriteriaGroupController.name);

  constructor(
    private readonly criteriaGroupService: CriteriaGroupService,
    private readonly organizationServiceSDK: OrganizationServiceSDK,
  ) {}

  @ApiQuery({
    name: 'searchText',
    required: false,
    description: 'Search text for filtering criteria groups',
  })
  @ApiQuery({
    name: 'includeCriteriaDetails',
    required: false,
    description:
      'Will exclude the criteria that are part of the group if false or not provided',
  })
  @Permissions(readCriteriaGroup)
  @Get('workspace/:workspaceId')
  async getCriteriaGroups(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('searchText') searchText: string | null,
    @Query('includeCriteriaDetails')
    includeCriteriaDetails: string | null,
  ): Promise<GetCriteriaGroups200Response> {
    return this.criteriaGroupService.getCriteriaGroups(
      organizationId,
      workspaceId,
      paginationOptions,
      searchText,
      includeCriteriaDetails,
    );
  }

  @Permissions(readCriteriaGroup)
  @Get('workspace/:workspaceId/criteria-group/:criteriaGroupId')
  async getSingleCriteriaGroup(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId') workspaceId: number,
    @Param('criteriaGroupId') criteriaGroupId: string,
  ): Promise<GetSingleCriteriaGroup200Response> {
    return this.criteriaGroupService.getSingleCriteriaGroup(
      organizationId,
      workspaceId,
      criteriaGroupId,
    );
  }

  @Permissions(createCriteriaGroup)
  @Post('workspace/:workspaceId')
  async createCriteriaGroup(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() createCriteriaGroupRequest: CreateCriteriaGroup,
    @Request() request: any,
  ) {
    await this.validateWorkspaceInOrganization(
      request.userId,
      organizationId,
      workspaceId,
    );

    return this.criteriaGroupService.createCriteriaGroup(
      organizationId,
      workspaceId,
      request.userId,
      createCriteriaGroupRequest,
    );
  }

  @ApiQuery({
    name: 'workspaceId',
    required: false,
    description:
      'Workspace id for validating user access to update criteria group',
  })
  @Permissions(updateCriteriaGroup)
  @Patch('workspace/:workspaceId/criteria-group/:criteriaGroupId')
  async updateCriteriaGroup(
    @Param('organizationId') organizationId: string,
    @Param('criteriaGroupId') criteriaGroupId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() createCriteriaGroupRequest: CreateCriteriaGroup,
    @Request() request: any,
  ) {
    await this.validateWorkspaceInOrganization(
      request.userId,
      organizationId,
      workspaceId,
    );

    return this.criteriaGroupService.editCriteriaGroup(
      organizationId,
      workspaceId,
      criteriaGroupId,
      createCriteriaGroupRequest,
    );
  }

  @ApiQuery({
    name: 'workspaceId',
    required: false,
    description:
      'Workspace id for validating user access to delete criteria group',
  })
  @Permissions(deleteCriteriaGroup)
  @Delete('workspace/:workspaceId/criteria-group/:criteriaGroupId')
  async deleteCriteriaGroup(
    @Param('organizationId') organizationId: string,
    @Param('criteriaGroupId') criteriaGroupId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Request() request: any,
  ) {
    await this.validateWorkspaceInOrganization(
      request.userId,
      organizationId,
      workspaceId,
    );

    return this.criteriaGroupService.deleteCriteriaGroup(
      organizationId,
      criteriaGroupId,
    );
  }

  @ApiQuery({
    name: 'workspaceId',
    required: false,
    description:
      'Workspace id for validating user access to delete criteria group',
  })
  @Permissions([updateCriteriaGroup, updateCriteriaGroupForWorkspace])
  @Post('workspace/:workspaceId/criteria-group/criteria')
  async manageCriteriaInCriteriaGroups(
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body()
    manageCriteriaGroupCriteriaRequest: ManageCriteriaGroupCriteriaRequest,
    @Request() request: any,
  ) {
    await this.validateWorkspaceInOrganization(
      request.userId,
      organizationId,
      workspaceId,
    );

    return this.criteriaGroupService.manageCriteriaInCriteriaGroups(
      organizationId,
      manageCriteriaGroupCriteriaRequest,
    );
  }

  private async validateWorkspaceInOrganization(
    userId: number,
    organizationId: string,
    workspaceId: number,
  ) {
    const { result } =
      await this.organizationServiceSDK.validateWorkspacesWithinOrganizationAsPromise(
        organizationId,
        userId,
        {
          workspaceIds: [workspaceId],
        },
      );

    if (!result.success) {
      const error = new ForbiddenException(
        `Workspace ids are not valid for organization ${organizationId}`,
      );
      this.logger.error(error);
      throw error;
    }
  }
}
