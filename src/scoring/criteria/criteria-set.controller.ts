import {
  Body,
  Controller,
  Delete,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Request,
  ValidationPipe,
} from '@nestjs/common';
import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  createCriteriaSet,
  deleteCriteriaSet,
  readCriteriaSet,
  updateCriteriaSet,
} from '../scoring.permissions';
import { CriteriaSetService } from './criteria-set.service';
import {
  CreateCriteriaSetDto,
  SearchCriteriaSetDto,
  UpdateCriteriaSetDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ApiBody, ApiParam, ApiTags } from '@nestjs/swagger';

@ApiTags('compliance-criteria-set')
@Controller('compliance/criteria-set')
export class CriteriaSetController {
  constructor(private criteriaSetService: CriteriaSetService) {}

  /**
   * Creates a criteria sets.
   */
  @Permissions(createCriteriaSet)
  @Post('')
  createCriteriaSet(@Body() createCriteriaSetDto: CreateCriteriaSetDto) {
    return this.criteriaSetService.createCriteriaSet(createCriteriaSetDto);
  }

  /**
   * Gets the criteria sets related to the given parameters
   * @param req
   * @param organizationId - The id of the organization.
   * @param searchQueryDto - The parameters to search for criteria sets.
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The unique identifier of the organization for which  criteria set exists in.',
  })
  @ApiBody({})
  @Permissions(readCriteriaSet)
  @Post('/organization/:organizationId')
  async getCriteriaSets(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(new ValidationPipe()) searchCriteriaSetDto: SearchCriteriaSetDto,
  ) {
    return this.criteriaSetService.getCriteriaSets(
      organizationId,
      searchCriteriaSetDto,
    );
  }

  /**
   * Updates a criteria set and its associations.
   * @param id - The id of the criterion.
   * @param updateCriteriaSetDto - The details of the criteria set to update.
   */
  @Permissions(updateCriteriaSet)
  @ApiParam({ name: 'id', description: 'The id of the criteria set' })
  @Put(':id')
  async updateCriteriaSet(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCriteriaSetDto: UpdateCriteriaSetDto,
  ) {
    return this.criteriaSetService.updateCriteriaSet(id, updateCriteriaSetDto);
  }

  /**
   * Deletes a criteria set.
   * @param id - The id of the criteria set.
   */
  @Permissions(deleteCriteriaSet)
  @ApiParam({ name: 'id', description: 'The id of the criteria set' })
  @Delete(':id')
  async deleteCriteriaSet(@Param('id', ParseIntPipe) id: number) {
    return this.criteriaSetService.deleteCriteriaSet(id);
  }
}
