import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  Param,
  ParseArrayPipe,
  ParseBoolPipe,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Request,
  Res,
  Version,
} from '@nestjs/common';
import { CriteriaService } from './criteria.service';
import {
  CriteriaCreateDto,
  GetCriteriaOptions200Response,
  GetCriteriaQueryParamsDto,
  SearchCriteriaDto,
  UpdateCriteriaDto,
  CriteriaAttributesDTO,
  UploadCustomIconDto,
  AttachIconsToCriteriaDto,
  DetachIconsFromCriteriaDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import {
  deleteCriteria,
  readConfiguration,
  readCriteria,
  updateConfiguration,
} from '../scoring.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { ApiBody, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { GetCriteriaInCriteriaSets200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getCriteriaInCriteriaSets200Response';
import { Response } from 'express';

@ApiTags('criteria')
@Controller('criteria')
export class CriteriaController {
  constructor(private criteriaService: CriteriaService) {}

  @Get('workspace/:workspaceId/current-best-practices')
  async getCurrentWorkspaceBestPractices(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('channels') channels: string,
  ) {
    return this.criteriaService.getBestPracticesSummaryForWorkspace(
      workspaceId,
      channels,
    );
  }

  @Get('workspace/:workspaceId/outdated-best-practices')
  async getOutdatedWorkspaceBestPractices(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('channels') channels: string,
  ) {
    return this.criteriaService.getOutdatedBestPracticesForWorkspace(
      workspaceId,
      channels,
    );
  }

  @Post(':workspaceId/create-best-practices')
  async createWorkspaceBestPractices(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('userId', ParseIntPipe) userId: number,
    @Body('bestPracticeDefinitionIds') bestPracticeDefinitionIds: number[],
  ) {
    return this.criteriaService.createWorkspaceBestPractices(
      userId,
      workspaceId,
      bestPracticeDefinitionIds,
    );
  }

  @Post('workspace/:workspaceId/delete-outdated-best-practices')
  async deleteOutdatedWorkspaceBestPractices(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body('bestPracticeDefinitionIds') bestPracticeDefinitionIds: number[],
  ) {
    return this.criteriaService.deleteOutdatedWorkspaceBestPractices(
      workspaceId,
      bestPracticeDefinitionIds,
    );
  }

  /**
   * Delete a criteria from a criteria set.
   * @param req
   * @param partnerId - The id of the partner.
   * @param criteriaId - The id of the criterion
   */
  @ApiParam({ name: 'partnerId', description: 'The id of the partner' })
  @ApiParam({ name: 'id', description: 'The id of the criteria' })
  @Permissions(updateConfiguration)
  @Delete(':criteriaId/partner/:partnerId')
  deleteCriteria(
    @Request() req: any,
    @Param('partnerId', ParseIntPipe) partnerId: number,
    @Param('criteriaId', ParseIntPipe) criteriaId: number,
  ) {
    const { userId } = req;
    return this.criteriaService.deleteCriteria(partnerId, criteriaId, userId);
  }

  /**
   * Create a new criteria as part of a criteria set or in the partner's default criteria set if not specified in
   * the body of the request.
   * @param workspaceId - The ID of the workspace.
   * @param createCriteriaDto - The details of the criteria to create.
   * @param req - The request context
   */
  @ApiParam({ name: 'workspaceId', description: 'The ID of the workspace' })
  @Permissions(updateConfiguration)
  @Post('workspace/:workspaceId')
  createCriteria(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() createCriteriaDto: CriteriaCreateDto,
    @Request() req: any,
  ) {
    const { userId } = req;
    return this.criteriaService.createCriteria(
      userId,
      workspaceId,
      createCriteriaDto,
    );
  }

  /*  @Permissions(readDetails)
  @Get('partner/:partnerId')
  getPartnerCriteriaSets(
    @Param('partnerId', ParseIntPipe) partnerId: number,
    @Query('detail', new DefaultValuePipe(false), ParseBoolPipe)
    detail?: boolean,
  ) {
    return this.criteriaService.getPartnerCriteriaSets(partnerId, detail);
  }*/

  @Permissions(readConfiguration)
  @Get('template/partner/:partnerId')
  getPartnerCriteriaTemplates(
    @Param('partnerId', ParseIntPipe) partnerId: number,
    @Query('excludeDeprecated', new DefaultValuePipe(false), ParseBoolPipe)
    excludeDeprecated: boolean,
  ) {
    return this.criteriaService.getPartnerCriteriaTemplates(
      partnerId,
      excludeDeprecated,
    );
  }

  /**
   * Gets the criteria related to the given parameters.
   * @param organizationId - The id of the organization.
   * @param searchQueryDto - The parameters to search for criteria.
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The unique identifier of the organization for which the criteria belongs to.',
  })
  @ApiBody({})
  @Permissions(readCriteria)
  @Post('/organization/:organizationId')
  getCriteria(
    @Param('organizationId') organizationId: string,
    @Body() searchQueryDto: SearchCriteriaDto,
  ) {
    return this.criteriaService.findCriteria(organizationId, searchQueryDto);
  }

  /**
   * Updates a criterion. For use in the criteria management initiative.
   * @param criteriaId
   * @param workspaceId
   * @param updateCriteriaDto
   * @param req
   */
  @Permissions(updateConfiguration)
  @ApiParam({ name: 'criteriaId', description: 'The id of the criterion' })
  @Patch('/:partnerId/:criteriaId')
  updateCriteria(
    @Param('criteriaId', ParseIntPipe) criteriaId: number,
    @Param('partnerId', ParseIntPipe) workspaceId: number,
    @Body() updateCriteriaDto: UpdateCriteriaDto,
    @Request() req: any,
  ) {
    const { userId } = req;

    return this.criteriaService.updateCriteria(
      criteriaId,
      updateCriteriaDto,
      userId,
      workspaceId,
    );
  }

  /**
   * Soft deletes a criterion. For use in the criteria management initiative.
   * @param id
   */
  @Permissions(deleteCriteria)
  @ApiParam({ name: 'id', description: 'The id of the criterion' })
  @Put(':id')
  deleteCriterion(@Param('id', ParseIntPipe) id: number) {
    return this.criteriaService.deleteCriterion(id);
  }

  @ApiQuery({
    name: 'workspaceId',
    description: 'System assigned Id of the workspace',
    required: true,
  })
  @Get()
  getWorkspaceCriteriaList(
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('workspaceId') workspaceId: number,
    @Query() getCriteriaQueryParamsDto: GetCriteriaQueryParamsDto,
    @Request() req: any,
  ): Promise<GetCriteriaInCriteriaSets200Response> {
    return this.criteriaService.getWorkspaceCriteriaList(
      req.userId,
      workspaceId,
      paginationOptions,
      getCriteriaQueryParamsDto,
    );
  }

  @Version('2')
  @Get()
  async getCriteriaAcrossWorkspacesV2(
    @Request() req: any,
    @GetPagination() pagination: PaginationOptions,
    @Query(
      'workspaceIds',
      new ParseArrayPipe({ items: Number, separator: ',' }),
    )
    workspaceIds: number[],
    @Query() queryParams: GetCriteriaQueryParamsDto,
  ) {
    return this.criteriaService.getMultipleWorkspaceCriteriaList(
      req.userId,
      workspaceIds,
      pagination,
      queryParams,
    );
  }

  @Post('download-csv')
  async downloadWorkspaceCriteriaCsv(
    @Body('workspaceIds', ParseArrayPipe) workspaceIds: string[],
    @Body('criteriaParams') criteriaParams: GetCriteriaQueryParamsDto,
    @Res() res: Response,
    @Request() req: any,
  ) {
    const csvBuffer = await this.criteriaService.getCriteriaAsCsv(
      req.userId,
      workspaceIds.map((id) => Number(id)),
      criteriaParams,
    );

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="workspace-criteria.csv"',
    );
    res.send(csvBuffer);
  }

  @ApiQuery({
    name: 'workspaceId',
    description: 'System assigned Id of the workspace',
    required: true,
  })
  @Get('options')
  async getCriteriaOptions(
    @Request() req: any,
    @Query('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('includeGlobalOptions', new DefaultValuePipe(false), ParseBoolPipe)
    includeGlobalOptions?: boolean,
  ): Promise<GetCriteriaOptions200Response> {
    return this.criteriaService.getWorkspaceCriteriaOptions(
      req.userId,
      workspaceId,
      includeGlobalOptions,
    );
  }

  @Post('/organization/:organizationId/details')
  async getCriteriaDetails(
    @Param('organizationId') organizationId: string,
    @Body() criteriaAttributesDto: CriteriaAttributesDTO,
  ) {
    return this.criteriaService.getCriteriaDetails(
      organizationId,
      criteriaAttributesDto,
    );
  }

  @Get('organization/:organizationId/custom-icons')
  async getCustomIconsForOrganization(
    @Param('organizationId') organizationId: string,
  ) {
    return this.criteriaService.getOrgCustomIcons(organizationId);
  }

  // upload single custom icon to org

  @Post('organization/:organizationId/custom-icons')
  async uploadCustomIcon(
    @Param('organizationId') organizationId: string,
    @Body() uploadDetails: UploadCustomIconDto,
  ) {
    return this.criteriaService.uploadCustomIcon(organizationId, uploadDetails);
  }

  // attach icon key to criteria record

  @Post('custom-icons/attach')
  async attachIconsToCriteria(
    @Body() iconCriteriaMappings: AttachIconsToCriteriaDto,
  ) {
    return this.criteriaService.attachIconsToCriteria(iconCriteriaMappings);
  }

  // detach icon key from criteria record

  @Post('custom-icons/detach')
  async detachIconsFromCriteria(
    @Body() detachIconsFromCriteriaDto: DetachIconsFromCriteriaDto,
  ) {
    return this.criteriaService.detachIconsFromCriteria(
      detachIconsFromCriteriaDto,
    );
  }
}
