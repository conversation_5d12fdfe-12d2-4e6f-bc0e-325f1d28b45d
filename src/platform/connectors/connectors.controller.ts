import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  createDataExportsPermission,
  readDataExportsPermission,
} from '../data-exports/data-exports.permissions';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ConnectorsDTO } from './dto/getDataExportsConnectorsResponse.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { CreateConnectorResponseDto } from './dto/createConnectorResponse.dto';
import { CreateConnectorRequestDto } from './dto/createConnectorRequest.dto';
import { ConnectorsService } from './connectors.service';
import { SearchOptions } from './dto/searchOptions.dto';
import { SortOptions } from './dto/sortOptions.dto';
import { CreateDataExportConnectorDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportConnectorDto';
import { CreateDataExportConnectorConfigurationDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportConnectorConfigurationDto';

@ApiTags('Connectors')
@Controller('connectors')
export class ConnectorsController {
  constructor(private connectorsService: ConnectorsService) {}

  @Permissions(readDataExportsPermission)
  @Get('organization/:organizationId')
  @ApiOperation({
    summary: 'Retrieve list of connectors for a given organization',
    description:
      'Fetches an array of connectors, so users can see what connectors are set up already',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns an array of connectors for a given organization',
    type: ConnectorsDTO,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error retrieving data exports connectors',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getDataExportsConnectors(
    @Param('organizationId') organizationId: string,
    @Query() paginationOptions: PaginationOptions,
    @Query() searchOptions: SearchOptions,
    @Query() sortOptions: SortOptions,
  ) {
    return await this.connectorsService.getDataExportsConnectorsForOrg(
      organizationId,
      paginationOptions,
      searchOptions,
      sortOptions,
    );
  }

  @Permissions(createDataExportsPermission)
  @Post('organization/:organizationId')
  @ApiOperation({
    summary: 'Create a new connector',
  })
  @ApiResponse({
    status: 201,
    description: 'Connector successfully created',
    type: CreateConnectorResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createConnector(
    @Param('organizationId') organizationId: string,
    @Body() body: CreateConnectorRequestDto,
    @Req() req: any,
  ) {
    const { userId } = req;
    const dto: CreateDataExportConnectorDto = {
      connectorName: body.connectorName,
      s3Config: {
        path: body.path,
        bucket: body.s3BucketName,
        roleArn: body.userRoleARN,
        service: CreateDataExportConnectorConfigurationDto.ServiceEnum.S3,
      },
    };
    return await this.connectorsService.createConnector(
      userId,
      organizationId,
      dto,
    );
  }
}
