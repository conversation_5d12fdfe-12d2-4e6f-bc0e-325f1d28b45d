import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { DataExportsService } from './data-exports.service';
import {
  FrontEndBasicReadReportDto,
  ReadDataExportReportOrganizationDto,
} from './dto/getAllDataExportsResponse.dto';
import { CreateDataExportBFFResponseDto } from './dto/createDataExportBFFResponse.dto';
import { CreateDataExportOrganizationRequestDto } from './dto/createDataExportBFFRequest.dto';
import {
  createDataExportsPermission,
  deleteDataExportsPermission,
  readDataExportsPermission,
} from './data-exports.permissions';
import { GetDataExportFilterQueryParamDto } from './dto/getDataExportFilterQueryParam.dto';
import { DataExportFilterValue } from '@vidmob/vidmob-organization-service-sdk';
import { ReadOneDataExportReportOrganizationDto } from './dto/getOneDataExportsResponse.dto';
import { CreateFilteredReportRequestDto } from './dto/createFilteredReportRequest.dto';
import { GetAllScheduledExportsRequestDto } from './dto/getAllScheduledExportsRequest.dto';

@ApiTags('Data Exports')
@Controller('data-exports')
export class DataExportsController {
  constructor(private dataExportsService: DataExportsService) {}

  @Permissions(readDataExportsPermission)
  @Post('organization/:organizationId/report/filter')
  @ApiOperation({
    summary:
      'Retrieve list of reports that can be exported for a given organization',
    description:
      'Fetches an array of report metadata, so users can see what reports are available and download any they need.',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns an array of reports available for export for a given organization',
    type: ReadDataExportReportOrganizationDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error retrieving data exports',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getAllDataExportsForOrgWithFilter(
    @Param('organizationId') organizationId: string,
    @Body() option: CreateFilteredReportRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return await this.dataExportsService.getFilteredReports(
      organizationId,
      option,
      paginationOptions,
    );
  }

  @Permissions(readDataExportsPermission)
  @Post('organization/:organizationId/scheduled-exports')
  @ApiOperation({
    summary: 'Retrieve list of scheduled exports for a given organization',
    description:
      'Fetches an array of scheduled export records so users can see information about and modify them.',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns an array of reports available for export for a given organization',
    type: ReadDataExportReportOrganizationDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error retrieving data exports',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getAllScheduledExports(
    @Param('organizationId') organizationId: string,
    @Body() option: GetAllScheduledExportsRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return await this.dataExportsService.getAllScheduledExports(
      organizationId,
      option,
      paginationOptions,
    );
  }

  @Permissions(readDataExportsPermission)
  @Get('organization/:organizationId')
  @ApiOperation({
    summary:
      'Retrieve list of reports that can be exported for a given organization',
    description:
      'Fetches an array of report metadata, so users can see what reports are available and download any they need.',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns an array of reports available for export for a given organization',
    type: FrontEndBasicReadReportDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error retrieving data exports',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getAllDataExportsForOrg(
    @Param('organizationId') organizationId: string,
    @Query() paginationOptions: PaginationOptions,
  ) {
    return await this.dataExportsService.getAllDataExportsForOrg(
      organizationId,
      paginationOptions,
    );
  }

  @Permissions(readDataExportsPermission)
  @Get('organization/:organizationId/report/:reportId')
  @ApiOperation({
    summary: 'Retrieve an export report by id for a given organization',
    description:
      'Fetches a single export report, allowing you to retrieve a download link for the report.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns a singe export report by id for a given organization',
    type: ReadOneDataExportReportOrganizationDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Report not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error retrieving data exports',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getDataExportById(
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
  ) {
    return await this.dataExportsService.getDataExportById(
      organizationId,
      reportId,
    );
  }

  @Permissions(deleteDataExportsPermission)
  @Delete('organization/:organizationId/report/:reportId')
  @ApiOperation({
    summary: 'Deletes an export report by id for a given organization',
  })
  @ApiResponse({
    status: 200,
    description: 'Deleted report successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Report not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error retrieving data exports',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async deleteDataExport(
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
  ) {
    return await this.dataExportsService.deleteReport(organizationId, reportId);
  }

  @Permissions(createDataExportsPermission)
  @Post('organization/:organizationId')
  @ApiOperation({
    summary: 'Create a new data export',
  })
  @ApiResponse({
    status: 201,
    description: 'Data Export successfully created',
    type: CreateDataExportBFFResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createDataExport(
    @Param('organizationId') organizationId: string,
    @Body() body: CreateDataExportOrganizationRequestDto,
    @Req() req: any,
  ) {
    const { userId } = req;
    return await this.dataExportsService.createOrganizationDataExport(
      userId,
      organizationId,
      body,
    );
  }

  @Get('filters/organization/:organizationId')
  @ApiResponse({
    status: 201,
    description: 'Filter was made and returned to use',
    type: () => [DataExportFilterValue],
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getFilters(
    @Param('organizationId') organizationId: string,
    @Query('filterOptions') query?: GetDataExportFilterQueryParamDto,
  ) {
    const startDate: string | undefined = query?.startDate;
    const endDate: string | undefined = query?.endDate;
    return this.dataExportsService.getFilterInformation(
      organizationId,
      startDate,
      endDate,
    );
  }
}
