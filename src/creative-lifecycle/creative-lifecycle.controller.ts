import { Controller, Post, Body, Query } from '@nestjs/common';
import { CreativeLifecycleService } from './creative-lifecycle.service';
import { GetCreativeLifecycleDto } from './dto/get-creative-lifecycle.dto';
import { Permissions } from '../auth/decorators/permission.decorator';
import { readOrganizationWorkspaceAdAccountReportsBodyHandler } from '../analytics/analytics.permissions';

@Controller('creative-lifecycle')
export class CreativeLifecycleController {
  constructor(
    private readonly creativeLifecycleService: CreativeLifecycleService,
  ) {}

  @Post('report')
  @Permissions(readOrganizationWorkspaceAdAccountReportsBodyHandler)
  getReportData(
    @Body() getCreativeLifecycleDto: GetCreativeLifecycleDto,
    @Query('useDB') useDB: boolean,
    @Query('isCoke') isCoke: boolean,
  ) {
    return this.creativeLifecycleService.getReportData(
      getCreativeLifecycleDto,
      useDB,
      isCoke,
    );
  }

  @Post('kpis')
  @Permissions(readOrganizationWorkspaceAdAccountReportsBodyHandler)
  getKpiData(
    @Body() getCreativeLifecycleDto: GetCreativeLifecycleDto,
    @Query('useDB') useDB: boolean,
  ) {
    return this.creativeLifecycleService.getKpiData(
      getCreativeLifecycleDto,
      useDB,
    );
  }

  // @Get()
  // findAll() {
  //   return this.creativeLifecycleService.findAll();
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.creativeLifecycleService.findOne(+id);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.creativeLifecycleService.remove(+id);
  // }
}
