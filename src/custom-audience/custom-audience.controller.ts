import { Controller, Get, Param, Query, Request } from '@nestjs/common';
import { CustomAudienceService } from './custom-audience.service';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
  VmApiOkUnPaginatedArrayResponse,
} from '@vidmob/vidmob-nestjs-common';
import {
  AccountType,
  CustomAudienceTargetingStatus,
  Platform,
} from '../constants/platform.constants';
import { ReadCustomAudienceDto } from './dto/read-custom-audience.dto';

/**
 * Custom Audience Controller
 */
@ApiTags('Custom Audience')
@Controller('custom-audience')
export class CustomAudienceController {
  constructor(private readonly customAudienceService: CustomAudienceService) {}

  /**
   * This endpoint returns all custom audiences for the account
   * @param req
   * @param platform Platform name
   * @param accountId Platform Account Id or Platform Account Group Id
   * @param name Custom audience name keyword
   * @param accountType Custom audience name keyword
   * @param paginationOptions
   * @returns list of custom audiences
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadCustomAudienceDto,
  })
  @ApiQuery({
    name: 'platform',
    enum: Platform,
    description: 'Platform name',
    required: true,
  })
  @ApiQuery({
    name: 'accountId',
    type: String,
    description: 'Platform Account Id or Platform Account Group Id',
    required: true,
  })
  @ApiQuery({
    name: 'name',
    type: String,
    description: 'Custom audience name keyword',
    required: false,
  })
  @ApiQuery({
    name: 'accountType',
    enum: AccountType,
    description:
      'Account type to know if custom audiences are get for an account or an account group',
    required: true,
  })
  @Get('')
  async findCustomAudiencesForAccount(
    @Request() req: any,
    @Query('platform') platform: Platform,
    @Query('accountId') accountId: string,
    @Query('name') name: string,
    @Query('accountType') accountType: AccountType,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const accountIds =
      await this.customAudienceService.getAccountIdsForCustomAudiencesRequest(
        req.headers.authorization,
        { accountType, accountId, platform },
      );

    return this.customAudienceService.findAllCustomAudiencesForAccounts(
      platform,
      accountIds,
      paginationOptions,
      name,
    );
  }

  /**
   * This endpoint returns a custom audience for the account
   * @param customAudienceId A custom audience id
   * @param platform Platform name
   * @param accountId Platform Account Id
   * @returns a custom audience
   */
  @VmApiOkResponse({
    type: ReadCustomAudienceDto,
  })
  @ApiParam({
    name: 'customAudienceId',
    type: String,
    description: 'Custom Audience Id',
    required: true,
  })
  @ApiQuery({
    name: 'platform',
    enum: Platform,
    description: 'Platform name',
    required: true,
  })
  @ApiQuery({
    name: 'accountId',
    type: String,
    description: 'Platform Account Id',
    required: true,
  })
  @Get(':customAudienceId')
  findCustomAudience(
    @Param('customAudienceId') customAudienceId: string,
    @Query('platform') platform: Platform,
    @Query('accountId') accountId: string,
  ) {
    return this.customAudienceService.getCustomAudience(
      platform,
      accountId,
      customAudienceId,
    );
  }

  /**
   * This endpoint returns additional custom audiences of a custom audience for an account
   * @param req
   * @param customAudienceId A custom audience id
   * @param platform Platform name
   * @param accountId Platform Account Id
   * @param targetingStatus Custom Audience targeting status
   * @param startDate Start date to get additional custom audiences for a single custom audience, format yyyy-mm-dd
   * @param endDate End date to get additional custom audiences for a single custom audience, format yyyy-mm-dd
   * @returns list of additional custom audiences for a custom audience
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: ReadCustomAudienceDto,
  })
  @ApiParam({
    name: 'customAudienceId',
    type: String,
    description: 'Custom Audience Id',
    required: true,
  })
  @ApiQuery({
    name: 'platform',
    enum: Platform,
    description: 'Platform name',
    required: true,
  })
  @ApiQuery({
    name: 'accountId',
    type: String,
    description: 'Platform Account Id',
    required: true,
  })
  @ApiQuery({
    name: 'targetingStatus',
    enum: CustomAudienceTargetingStatus,
    description: 'Custom Audience targeting status',
    required: true,
  })
  @ApiQuery({
    name: 'startDate',
    type: String,
    description:
      'Start date to get additional custom audiences for a single custom audience, format yyyy-mm-dd',
    required: true,
  })
  @ApiQuery({
    name: 'endDate',
    type: String,
    description:
      'End date to get additional custom audiences for a single custom audience, format yyyy-mm-dd',
    required: true,
  })
  @Get(':customAudienceId/additional-audience')
  findAdditionalCustomAudiences(
    @Request() req: any,
    @Param('customAudienceId') customAudienceId: string,
    @Query('platform') platform: Platform,
    @Query('accountId') accountId: string,
    @Query('targetingStatus') targetingStatus: CustomAudienceTargetingStatus,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    const tokenWithBearerPrefix = req.headers.authorization;
    return this.customAudienceService.getAdditionalCustomAudiences(
      platform,
      accountId,
      customAudienceId,
      targetingStatus,
      tokenWithBearerPrefix,
      startDate,
      endDate,
    );
  }
}
