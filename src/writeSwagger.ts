import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { UserResponseDto } from './dto/user-response.dto';
import { BaseResponseDto } from './platform/api-key-management/dto/base-response.dto';
import {
  API_DESCRIPTION,
  API_TITLE,
  API_VERSION,
} from './constants/api.constants';
import { writeFileSync } from 'fs';
import * as yaml from 'js-yaml';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const config = new DocumentBuilder()
    .setTitle(API_TITLE)
    .setDescription(API_DESCRIPTION)
    .setVersion(API_VERSION)
    .addServer('https://bff-dev.vidmob.com', 'Dev')
    .addServer('https://bff-stage.vidmob.com', 'Stage')
    .addServer('https://bff.vidmob.com', 'Prod')
    .addServer('http://localhost:3000', 'Local')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'Bearer Token',
        name: 'Authorization',
        description: 'Enter your Bearer Token (without "Bearer " prefix)',
        in: 'header',
      },
      'Bearer Token', // This is the key that will be used to reference this auth method
    )
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    extraModels: [UserResponseDto, BaseResponseDto],
  });
  const docYaml = yaml.dump(document);
  writeFileSync('oas.yaml', docYaml);

  await app.close();
}
bootstrap();
