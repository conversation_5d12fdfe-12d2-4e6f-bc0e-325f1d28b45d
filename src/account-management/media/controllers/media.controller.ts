import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { MediaService } from '../services/media.service';
import { Authorities } from '../../../auth/decorators/authority.decorator';

/**
 * The controller responsible for listing out organizations for media.
 */
@ApiTags('Media')
@Controller('account-management/media')
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  /**
   * Find all the organizations that are mapped to ad accounts related to media.
   * @param mediaId The media id.
   */
  @ApiParam({ name: 'mediaId', description: 'The id of the Media' })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Get(':mediaId/organizations')
  async findOrganizationsByMediaId(@Param('mediaId') mediaId: number) {
    return this.mediaService.findOrganizationsByMediaId(mediaId);
  }
}
