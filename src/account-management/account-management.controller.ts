import { Controller, Get, Query, Request } from '@nestjs/common';
import { ApiQuery, ApiTags } from '@nestjs/swagger';
import { WorkspaceService } from './organization/workspace/services/workspace.service';
import { AllUserWorkspaceDto } from './organization/workspace/dto/read-all-user-workspace.dto';
import { VmApiOkUnPaginatedArrayResponse } from '@vidmob/vidmob-nestjs-common';

@ApiTags('All Workspace')
@Controller('account-management/workspace/all')
export class WorkspaceAllController {
  constructor(private readonly workspaceService: WorkspaceService) {}

  /**
   * Returns all workspaces for a user across all orgs
   *
   * @returns AllUserWorkspaceDto[]
   */
  @VmApiOkUnPaginatedArrayResponse({
    type: AllUserWorkspaceDto,
    description: 'Returns all workspaces for user.',
  })
  @ApiQuery({
    name: 'organizationId',
    type: String,
    description: 'System assigned id of the VidMob organization',
    required: false,
  })
  @ApiQuery({
    name: 'feature',
    type: String,
    description:
      'Workspace feature identifiers to filter by. Example "BRAND-GOVERNANCE,CREATIVE-INTELLIGENCE"',
    required: false,
  })
  @Get()
  async findUserWorkspacesForAllOrgs(
    @Request() req: any,
    @Query('organizationId') organizationId?: string,
    @Query('feature')
    workspaceFeatures?: string,
  ) {
    const { userId } = req;
    return this.workspaceService.findUserWorkspacesForAllOrgs(
      userId,
      organizationId,
      workspaceFeatures,
    );
  }
}
