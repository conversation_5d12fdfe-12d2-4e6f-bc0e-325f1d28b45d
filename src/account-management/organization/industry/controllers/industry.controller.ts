import { ApiTags } from '@nestjs/swagger';
import { Controller, Get, Param } from '@nestjs/common';
import { IndustryService } from '../services/industry.service';
import { Permissions } from '../../../../auth/decorators/permission.decorator';
import { readIndustryData } from '../../../account-management.permissions';
import { VmApiOkUnPaginatedArrayResponse } from '@vidmob/vidmob-nestjs-common';
import { IndustryResponseDTO } from '../dto/read-industry.dto';

@ApiTags('Industry')
@Controller('account-management/organization/:organizationId')
export class IndustryController {
  constructor(private industryService: IndustryService) {}

  @VmApiOkUnPaginatedArrayResponse({
    type: IndustryResponseDTO,
    description: 'Returns all industry groups.',
  })
  @Permissions(readIndustryData)
  @Get('industry-groups')
  getIndustryGroups(@Param('organizationId') organizationId: string) {
    return this.industryService.getIndustryGroups(organizationId);
  }

  @VmApiOkUnPaginatedArrayResponse({
    type: IndustryResponseDTO,
    description: 'Returns all industries based on industry group id.',
  })
  @Permissions(readIndustryData)
  @Get('industry-groups/:industryGroupId/industries')
  getIndustries(
    @Param('organizationId') organizationId: string,
    @Param('industryGroupId') industryGroupId: number,
  ) {
    return this.industryService.getIndustries(organizationId, industryGroupId);
  }

  @VmApiOkUnPaginatedArrayResponse({
    type: IndustryResponseDTO,
    description: 'Returns all sub-industries based on industry id.',
  })
  @Permissions(readIndustryData)
  @Get('industries/:industryId/sub_industries')
  getSubIndustries(
    @Param('organizationId') organizationId: string,
    @Param('industryId') industryId: number,
  ) {
    return this.industryService.getSubIndustries(organizationId, industryId);
  }
}
