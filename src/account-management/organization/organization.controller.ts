import { <PERSON>, Get, Param, ParseArrayPipe, Query } from '@nestjs/common';
import { Request } from '@nestjs/common';
import { ApiQuery, ApiParam } from '@nestjs/swagger';
import { OrganizationService } from './services/organization.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
} from '@vidmob/vidmob-nestjs-common';
import { OrganizationWithWorkspaceCountDto } from './dto/organization-with-workspace-count.dto';

@Controller('account-management/organization')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  /**
   * Notice for GET findAllByUser():
   *
   * We will not use permissions for this particular endpoint because the organizations list is a top-level structure.
   * However, when accessing the organization service, the user's userId should be associated
   * with the 'organizationPersonRole' table with roles ORG_ADMIN or ORG_STANDARD.
   * If not, the service will return an error message indicating that the user does not have
   * permission to access the data.
   *
   * Note: Other endpoints in this controller might have different permission requirements.
   */

  /**
   * Returns a paginated list of organizations associated with a given user ID,
   * along with a count of workspaces the user is associated with in each organization.
   * @param userId The user's ID.
   * @param search Search string on the organization name or id.
   * @param paginationOptions Pagination options.
   */
  @VmApiOkPaginatedArrayResponse({
    type: OrganizationWithWorkspaceCountDto,
  })
  @ApiParam({
    name: 'userId',
    type: 'number',
    description: 'ID of the User',
  })
  @ApiQuery({
    name: 'search',
    type: 'string',
    required: false,
    description: 'Search string on the organization name or id',
  })
  @ApiQuery({
    name: 'perPage',
    type: 'number',
    required: false,
    description: 'Number of organizations to return per page',
  })
  @ApiQuery({
    name: 'offset',
    type: 'number',
    required: false,
    description: 'Offset for pagination',
  })
  @Get()
  findAllByUser(
    @Request() req: any,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('search') search?: string,
  ) {
    const { userId } = req;

    return this.organizationService.findAllByUserId(
      userId,
      search,
      paginationOptions,
    );
  }

  @Get(':organizationId/whitelist-features')
  async getOrganizationWhitelistFeatures(
    @Param('organizationId') organizationId: string,
    @Query('features', ParseArrayPipe) features: string[],
  ) {
    return this.organizationService.getOrgFeatureRecord(
      organizationId,
      features,
    );
  }
}
