import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  Version,
} from '@nestjs/common';
import { ReportsService } from './reports.service';
import { ReportCreationResponseDto } from './model/report-creation-response.dto';
import { ReportMetadataDto } from './model/report-metadata.dto';
import { ReportConverter } from './util/report-converter';
import {
  InflightReportCreationRequestDto,
  ReportCreationRequestDto,
} from './model/report-creation-request.dto';
import {
  InflightUpdateRequestDto,
  ReportUpdateRequestDto,
} from './model/report-update-request.dto';
import { SortByValidationPipe } from '../pagination-validators/sort-by-validation-pipe';
import { SortOrderValidationPipe } from '../pagination-validators/sort-order-validation-pipe';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Permissions } from '../auth/decorators/permission.decorator';
import {
  createReports,
  readOrgReports,
  readReports,
} from './reports.permissions';
import { ReportTypesValidationPipe } from './validator/report-types-validation-pipe';
import { ScoringAuthService } from '../scoring/scoring-auth/scoring-auth.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ReportMetadataListItemDto } from './model/report-metadata-list-item.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ScoringReportType } from './model/report';
import decorateReportMetadata from './model/decorate-report-metadata';
import { SortBy } from './reports-enums';
import { InflightMetadataResponseDto } from './model/inflight-metadata-response.dto';
import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';
import { GetScoringReportBodyDto } from './model/report-list.dto';

@ApiTags('Report')
@Controller({
  path: 'report',
})
export class ReportsController {
  constructor(
    private readonly reportsService: ReportsService,
    private readonly reportConverter: ReportConverter,
    private readonly scoringAuthService: ScoringAuthService,
  ) {}

  /**
   * Create a new report.
   * @param req The Express request object.
   * @param workspaceId The workspace to create the report for.
   * @param type The type of report to create.
   * @returns A Promise of ReportCreationResponseDto.
   */
  @Get('create/workspace/:workspaceId')
  @VmApiOkResponse({
    description: 'New default report metadata was successfully created',
    type: ReportCreationResponseDto,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The workspace to get the defaults for',
  })
  @ApiQuery({
    name: 'type',
    description: 'The type of report to create',
  })
  @Permissions(readReports)
  async create(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('type') type: ScoringReportType,
    @Request() req: any,
  ): Promise<ReportCreationResponseDto> {
    const userId = req['userId'] as number;
    const userInfo = { userId, workspaceId };
    return this.reportsService.createNewReport(type, userInfo);
  }

  @Get('create/workspace/:workspaceId')
  @Version('2')
  @VmApiOkResponse({
    description: 'New default report metadata was successfully created',
    type: ReportCreationResponseDto,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The workspace to get the defaults for',
  })
  @ApiQuery({
    name: 'type',
    description: 'The type of report to create',
  })
  @Permissions(readReports)
  async createV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('type') type: ScoringReportType,
    @Request() req: any,
  ): Promise<ReportCreationResponseDto> {
    const userId = req['userId'] as number;
    const userInfo = { userId, workspaceId };
    return this.reportsService.createNewReportV2(type, userInfo);
  }

  @Get('create/workspace/:workspaceId')
  @Version('3')
  @VmApiOkResponse({
    description: 'New default report metadata was successfully created',
    type: ReportCreationResponseDto,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The workspace to get the defaults for',
  })
  @ApiQuery({
    name: 'type',
    description: 'The type of report to create',
  })
  @Permissions(readReports)
  async createV3(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('type') type: ScoringReportType,
    @Request() req: any,
  ): Promise<ReportCreationResponseDto> {
    const userId = req['userId'] as number;
    const userInfo = { userId, workspaceId };
    return this.reportsService.createNewReportV3(type, userInfo);
  }

  @ApiParam({
    name: 'workspaceId',
    description: 'The workspace we get its saved reports for',
  })
  @ApiQuery({
    name: 'types',
    description: 'An array of report types to filter by.',
  })
  @ApiQuery({
    name: 'offset',
    description: 'The query offset',
  })
  @ApiQuery({
    name: 'perPage',
    description: 'The number of results',
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The order of the sorting ASC / DESC',
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'The field to sort the results by',
  })
  @ApiQuery({
    name: 'searchTerm',
    description: 'Filtering results by search term',
  })
  @VmApiOkPaginatedArrayResponse({
    type: ReportMetadataListItemDto,
  })
  @Permissions(readReports)
  @Get('workspace/:workspaceId')
  async getReports(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('types', ReportTypesValidationPipe) types: ScoringReportType[],
    @Query('sortOrder', SortOrderValidationPipe) sortOrder: 'ASC' | 'DESC',
    @Query('sortBy', SortByValidationPipe) sortBy: SortBy = SortBy.DateCreated,
    @Query('searchTerm') searchTerm: string,
  ): Promise<PaginatedResultArray<ReportMetadataListItemDto>> {
    return this.reportsService.getReports({
      workspaceId,
      paginationOptions,
      sortOrder,
      sortBy,
      searchTerm,
      filtersVersions: [1],
      customFilters: { types },
    });
  }

  @Permissions(readReports)
  @Get('workspace/:workspaceId')
  @Version('2')
  async getReportsV2(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('types', ReportTypesValidationPipe) types: ScoringReportType[],
    @Query('sortOrder', SortOrderValidationPipe) sortOrder: 'ASC' | 'DESC',
    @Query('sortBy', SortByValidationPipe) sortBy: SortBy = SortBy.DateCreated,
    @Query('searchTerm') searchTerm: string,
    @Request() req: any,
  ): Promise<PaginatedResultArray<ReportMetadataListItemDto>> {
    const userId = req['userId'] as number;

    // for v2 reports, we read the accessible workspaces and use them to filter the reports.

    const accessibleWorkspaceIds = (
      await this.scoringAuthService.getRelatedAccessiblePartners(
        userId,
        workspaceId,
      )
    ).map((workspace) => workspace.id);

    return this.reportsService.getReports({
      workspaceId,
      paginationOptions,
      sortOrder,
      sortBy,
      searchTerm,
      filtersVersions: [2],
      accessibleWorkspaceIds,
      customFilters: { types },
    });
  }

  @Permissions(readReports)
  @Post('workspace/:workspaceId')
  @Version('2')
  async getReportsV2WithFilters(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('sortOrder', SortOrderValidationPipe) sortOrder: 'ASC' | 'DESC',
    @Query('sortBy', SortByValidationPipe) sortBy: SortBy = SortBy.DateCreated,
    @Query('searchTerm') searchTerm: string,
    @Request() req: any,
    @Body() customFilters: GetScoringReportBodyDto,
  ): Promise<PaginatedResultArray<ReportMetadataListItemDto>> {
    const userId = req['userId'] as number;

    // for v2 reports, we read the accessible workspaces and use them to filter the reports.

    const accessibleWorkspaceIds = (
      await this.scoringAuthService.getRelatedAccessiblePartners(
        userId,
        workspaceId,
      )
    ).map((workspace) => workspace.id);

    return this.reportsService.getReports({
      workspaceId,
      paginationOptions,
      sortOrder,
      sortBy,
      searchTerm,
      filtersVersions: [2],
      accessibleWorkspaceIds,
      customFilters,
    });
  }

  @Permissions(readReports)
  @Post('workspace/:workspaceId')
  @Version('3')
  async getReportsV3WithFilters(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Query('sortOrder', SortOrderValidationPipe) sortOrder: 'ASC' | 'DESC',
    @Query('sortBy', SortByValidationPipe) sortBy: SortBy = SortBy.DateCreated,
    @Query('searchTerm') searchTerm: string,
    @Request() req: any,
    @Body() customFilters: GetScoringReportBodyDto,
  ): Promise<PaginatedResultArray<ReportMetadataListItemDto>> {
    const userId = req['userId'] as number;

    const accessibleWorkspaceIds = (
      await this.scoringAuthService.getRelatedAccessiblePartners(
        userId,
        workspaceId,
      )
    ).map((workspace) => workspace.id);

    return this.reportsService.getReports({
      workspaceId,
      paginationOptions,
      sortOrder,
      sortBy,
      searchTerm,
      filtersVersions: [2, 3],
      accessibleWorkspaceIds,
      customFilters,
    });
  }

  /**
   * Get the metadata of a specific report.
   *
   * @param reportId The ID of the report to fetch.
   * @returns A Promise of ReportMetadataDto or null if the report is not found.
   */
  @ApiParam({
    name: 'reportId',
    description: 'The id of the report to fetch',
  })
  @Get(':reportId/metadata')
  async getReportMetadata(
    @Param('reportId') reportId: string,
  ): Promise<ReportMetadataDto> {
    const reportMetadata = await this.reportsService.getReportMetadata(
      reportId,
    );
    return decorateReportMetadata(reportMetadata);
  }

  @ApiParam({
    name: 'reportId',
    description: 'The id of the report to fetch',
  })
  @Get(':reportId/metadata')
  @Version('2')
  async getReportMetadataV2(
    @Param('reportId') reportId: string,
  ): Promise<ReportMetadataDto> {
    // for now, the only difference with v1 is excluding defaults.
    return await this.reportsService.getReportMetadata(reportId);
  }

  @ApiParam({
    name: 'reportId',
    description: 'The id of the report to fetch',
  })
  @ApiQuery({
    name: 'includeInflightFiltersDisplayNames',
    description:
      'a flag to include the display names of the filters in the response if inflight report',
    required: false,
  })
  @Permissions(readOrgReports)
  @Get(':reportId/organization/:organizationId/metadata')
  @Version('3')
  async getReportMetadataV3(
    @Param('reportId') reportId: string,
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Query('includeInflightFiltersDisplayNames')
    includeInflightFiltersDisplayNames: string | undefined,
  ): Promise<ReportMetadataDto | InflightMetadataResponseDto> {
    // handles fetching inflight report metadata with filter display names, same as v2 for other reports
    const reportMetadata = await this.reportsService.getReportMetadata(
      reportId,
      organizationId,
    );
    const userDetails = {
      organizationId,
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    if (
      reportMetadata.reportType === ScoringReportType.IN_FLIGHT &&
      Boolean(includeInflightFiltersDisplayNames == 'true')
    ) {
      return this.reportsService.updateInflightReportMetadataWithFilterIdAndNames(
        reportMetadata,
        userDetails,
      );
    }

    return reportMetadata;
  }

  @ApiParam({
    name: 'reportId',
    description: 'The id of the report to fetch',
  })
  @ApiQuery({
    name: 'includeInflightFiltersDisplayNames',
    description:
      'a flag to include the display names of the filters in the response if inflight report',
    required: false,
  })
  @Permissions(readOrgReports)
  @Get(':reportId/organization/:organizationId/metadata')
  @Version('4')
  async getReportMetadataV4(
    @Param('reportId') reportId: string,
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Query('includeInflightFiltersDisplayNames')
    includeInflightFiltersDisplayNames: string | undefined,
  ): Promise<ReportMetadataDto | InflightMetadataResponseDto> {
    const reportMetadata = await this.reportsService.getReportMetadata(
      reportId,
      organizationId,
      true,
    );
    const userDetails = {
      organizationId,
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    if (
      reportMetadata.reportType === ScoringReportType.IN_FLIGHT &&
      Boolean(includeInflightFiltersDisplayNames === 'true')
    ) {
      return this.reportsService.updateInflightReportMetadataWithFilterIdAndNames(
        reportMetadata,
        userDetails,
      );
    }

    return reportMetadata;
  }

  /**
   * Save the metadata of a report.
   *
   * @param req The Express request object.
   * @param reportCreationRequest The report creation request containing the metadata.
   * @returns A Promise of ReportMetadataDto representing the saved report metadata.
   */
  @Permissions(createReports)
  @Post('metadata')
  async saveReportMetadata(
    @Request() req: any,
    @Body()
    reportCreationRequest: ReportCreationRequestDto,
  ): Promise<ReportMetadataDto> {
    const { userId } = req;
    let report;

    if (reportCreationRequest.reportType == ScoringReportType.IN_FLIGHT) {
      throw new Error(
        'Inflight report creation is not supported, use version 2 api with organizationId',
      );
    } else {
      report = await this.reportsService.save(
        userId,
        reportCreationRequest as ReportCreationRequestDto,
      );
    }

    return this.reportConverter.convert(report);
  }

  /**
   * Save the metadata of a report.
   *
   * @param organizationId
   * @param req The Express request object.
   * @param reportCreationRequest The report creation request containing the metadata.
   * @returns A Promise of ReportMetadataDto representing the saved report metadata.
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization workspaces and accounts belong to',
  })
  @Permissions(createReports)
  @Post('organization/:organizationId/metadata')
  @Version('2')
  async saveReportMetadataV2(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body()
    reportCreationRequest:
      | ReportCreationRequestDto
      | InflightReportCreationRequestDto,
  ): Promise<ReportMetadataDto> {
    const { userId } = req;
    let report;

    const userDetails = {
      organizationId,
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    if (reportCreationRequest.reportType == ScoringReportType.IN_FLIGHT) {
      const platformAccountIds =
        await this.scoringAuthService.validateAndReturnPlatformAccountIdsForInflightReport(
          (reportCreationRequest as InflightReportCreationRequestDto).filters,
          userDetails,
        );

      if (!platformAccountIds?.length) {
        throw new Error('No platform account ids found for request');
      }

      report = await this.reportsService.saveInflightReport(
        userId,
        reportCreationRequest as InflightReportCreationRequestDto,
        platformAccountIds,
      );
    } else {
      report = await this.reportsService.save(
        userId,
        reportCreationRequest as ReportCreationRequestDto,
      );
    }

    return this.reportConverter.convert(report);
  }

  /**
   * Update the metadata of a report.
   *
   * @param req The Express request object.
   * @param reportId The ID of the report to update.
   * @param reportUpdateRequest The partial report update request containing the metadata to update.
   * @returns A Promise of ReportMetadataDto representing the updated report metadata.
   */
  @ApiParam({
    name: 'reportId',
    description: 'The id of the report to update',
  })
  @Patch(':reportId/metadata')
  async updateReportMetadata(
    @Request() req: any,
    @Param('reportId') reportId: string,
    @Body()
    reportUpdateRequest: Partial<ReportUpdateRequestDto>,
  ): Promise<ReportMetadataDto> {
    const { userId } = req;
    const reportToUpdateFromDb =
      await this.reportsService.fetchReportToUpdateForUser(reportId, userId);

    if (reportToUpdateFromDb.reportType == ScoringReportType.IN_FLIGHT) {
      throw new Error(
        'Inflight report update is not supported, use version 2 api with organizationId',
      );
    }

    const updatedReport = await this.reportsService.updateNonInflightReport(
      reportToUpdateFromDb,
      reportUpdateRequest,
    );
    return this.reportConverter.convert(updatedReport);
  }

  /**
   * Update the metadata of a report.
   *
   * @param req The Express request object.
   * @param organizationId
   * @param reportId The ID of the report to update.
   * @param reportUpdateRequest The partial report update request containing the metadata to update.
   * @returns A Promise of ReportMetadataDto representing the updated report metadata.
   */
  @ApiParam({
    name: 'reportId',
    description: 'The id of the report to update',
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization workspaces and accounts belong to',
  })
  @Patch(':reportId/organization/:organizationId/metadata')
  @Version('2')
  async updateReportMetadataV2(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('reportId') reportId: string,
    @Body()
    reportUpdateRequest:
      | Partial<ReportUpdateRequestDto>
      | Partial<InflightUpdateRequestDto>,
  ): Promise<ReportMetadataDto> {
    const { userId } = req;
    const reportToUpdateFromDb =
      await this.reportsService.fetchReportToUpdateForUser(reportId, userId);

    let updatedReport;
    if (reportToUpdateFromDb.reportType == ScoringReportType.IN_FLIGHT) {
      if (reportUpdateRequest.filters) {
        (
          reportUpdateRequest.filters as InflightAggregateRequestDto
        ).platformAccountIds =
          await this.scoringAuthService.validateAndReturnPlatformAccountIdsForInflightReport(
            reportUpdateRequest.filters as InflightAggregateRequestDto,
            {
              organizationId,
              userId: req.userId,
              authorization: req.headers.authorization,
            },
          );
      }

      if (
        !(reportUpdateRequest.filters as InflightAggregateRequestDto)
          .platformAccountIds?.length
      ) {
        throw new Error('No platform account ids found for request');
      }

      updatedReport = await this.reportsService.updateInflightReport(
        reportToUpdateFromDb,
        reportUpdateRequest as InflightUpdateRequestDto,
      );
    } else {
      updatedReport = await this.reportsService.updateNonInflightReport(
        reportToUpdateFromDb,
        reportUpdateRequest as ReportUpdateRequestDto,
      );
    }

    return this.reportConverter.convert(updatedReport);
  }

  /**
   * Delete a report.
   *
   * @param req The Express request object.
   * @param reportId The ID of the report to delete.
   */
  @ApiParam({
    name: 'reportId',
    description: 'The id of the report to delete',
  })
  @Delete(':reportId')
  deleteReport(@Request() req: any, @Param('reportId') reportId: string) {
    const { userId } = req;
    return this.reportsService.deleteReport(userId, reportId);
  }
}
