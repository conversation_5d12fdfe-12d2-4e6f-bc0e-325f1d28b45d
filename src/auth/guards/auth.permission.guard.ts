import {
  CanActivate,
  ExecutionContext,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import {
  PermissionSpecification,
  PERMISSION_PARAM_KEY,
} from '../decorators/permission.decorator';
import { Reflector } from '@nestjs/core';
import {
  AuthorizationService,
  CanAccess200Response,
  StatementPermissionDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { PermissionDomain } from '../enums/permission.domain.enum';
import { firstValueFrom, Observable } from 'rxjs';
import { AxiosResponse, HttpStatusCode } from 'axios';
import { setTagOnParentSpan } from '../../utils/trace-utils';

@Injectable()
export class AuthPermissionsGuard implements CanActivate {
  private readonly logger = new Logger(AuthPermissionsGuard.name);

  constructor(
    readonly reflector: Reflector,
    private readonly authorizationService: AuthorizationService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const permissionSpecification = this.reflector.get<PermissionSpecification>(
      PERMISSION_PARAM_KEY,
      context.getHandler(),
    );

    if (!permissionSpecification) {
      return true;
    }

    if (Array.isArray(permissionSpecification)) {
      const promises = permissionSpecification.map((permission) =>
        this.checkSinglePermission(context, permission),
      );
      const canAccessArr = await Promise.all(promises);
      return canAccessArr.some((canAccess) => canAccess);
    }

    return await this.checkSinglePermission(context, permissionSpecification);
  }

  async checkSinglePermission(
    context: ExecutionContext,
    permissionSpecification: PermissionSpecification,
  ) {
    if (permissionSpecification.required) {
      const statements: StatementPermissionDto[] =
        permissionSpecification.required.map((req) => {
          return {
            action: req.action,
            subresource: req.subresource,
          } as StatementPermissionDto;
        });
      const request = context.switchToHttp().getRequest<any>();
      const domainId = permissionSpecification.domainContextHandler(context);

      this.attachTraceInfoOnRequest(
        permissionSpecification.domain,
        request,
        domainId,
      );
      try {
        let canAccess = false;
        if (Array.isArray(domainId)) {
          canAccess = await this.canAccessMultiple(
            permissionSpecification.domain,
            domainId,
            request.headers.authorization,
            statements,
          );
        } else {
          canAccess = await this.canAccess(
            permissionSpecification.domain,
            domainId,
            request.headers.authorization,
            statements,
          );
        }
        this.attachTraceInfoCanAccess(
          permissionSpecification.domain,
          canAccess,
        );
        return canAccess;
      } catch (error) {
        const msgError = `An unexpected error occurred trying to check permission: ${error.message}.`;
        this.logger.error(msgError);
        throw new InternalServerErrorException(msgError);
      }
    }

    return true;
  }

  async canAccessMultiple(
    domain: PermissionDomain,
    domainIds: string[] | number[],
    authorization: string,
    checkAccess: StatementPermissionDto[],
  ): Promise<boolean> {
    const domainChecks = domainIds.map(async (domainId) => {
      return this.canAccess(domain, domainId, authorization, checkAccess);
    });

    const results = await Promise.all(domainChecks);
    return results.every((result) => result === true);
  }

  async canAccess(
    domain: PermissionDomain,
    domainId: number | string,
    authorization: string,
    checkAccess: StatementPermissionDto[],
  ): Promise<boolean> {
    this.authorizationService.defaultHeaders.authorization = authorization;
    const responseObservable: Observable<AxiosResponse<CanAccess200Response>> =
      this.authorizationService.canAccess({
        domain,
        domainId,
        authorization,
        checkAccess,
      });
    try {
      const response: AxiosResponse = await firstValueFrom(responseObservable);
      return response?.data?.result?.canAccess;
    } catch (error) {
      const errorStatus = error?.response?.status;
      if (
        errorStatus === HttpStatusCode.Unauthorized ||
        errorStatus === HttpStatusCode.Forbidden
      ) {
        return false;
      }
      throw error;
    }
  }

  protected attachTraceInfoOnRequest(
    domain: string,
    req: any,
    domainId: number | string | number[] | string[],
  ) {
    setTagOnParentSpan(
      new Map<string, any>([
        [`request.permission.${domain}.'Id`, domainId],
        ['request.permission.userId', req['userId']],
        ['request.permission.username', req['username']],
      ]),
    );
  }

  protected attachTraceInfoCanAccess(domain: string, canAccess: boolean) {
    setTagOnParentSpan(
      new Map<string, any>([[`canAccess.permission.${domain}`, canAccess]]),
    );
  }
}
