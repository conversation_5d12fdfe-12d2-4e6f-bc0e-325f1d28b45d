import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  HttpStatus,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import {
  AuthorizationResponseDto,
  AuthorizationService,
} from '@vidmob/vidmob-authorization-service-sdk';
import { Observable, catchError, map, of } from 'rxjs';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { setTagOnParentSpan } from '../../utils/trace-utils';

@Injectable()
export class AuthTokenGuard implements CanActivate {
  private readonly logger = new Logger(AuthTokenGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly authorizationService: AuthorizationService,
  ) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    if (this.isPublic(context)) {
      return true;
    }
    const request = context.switchToHttp().getRequest();

    const authorization = request.headers.authorization;
    this.verifyToken(authorization);

    return this.authorizeRequest(request);
  }

  verifyToken(authorization: string) {
    if (!authorization) {
      throw new UnauthorizedException('Authorization not provided');
    }

    const token = authorization.split('Bearer')[1];

    if (!token) {
      throw new UnauthorizedException('Authorization not provided');
    }
  }

  authorizeRequest(request: any): Observable<boolean> {
    const authorization = request.headers.authorization;
    const originalUrl = request.originalUrl || request.url;
    const authObservable = this.authorizationService.authorize({
      authorization,
    });

    return authObservable.pipe(
      map((auth) => {
        this.attachAuthInfo(request, auth.data.result);
        return true;
      }),
      catchError((err) => {
        if (this.isUnauthorizedResponse(err)) {
          this.logger.warn(
            `Authorization failed for ${originalUrl}: ${
              err.response?.status
            } - ${err.response?.data?.message || err.message}`,
          );
          return of(false);
        }

        if (this.isBadRequestResponse(err)) {
          throw new BadRequestException('Invalid authorization header.');
        }
        this.logger.error(
          `Unexpected error during authorization for ${originalUrl}:`,
          err,
        );
        throw err;
      }),
    );
  }

  isUnauthorizedResponse(err: any): boolean {
    if (err.response && err.response.status) {
      const status = err.response.status;
      return (
        status === HttpStatus.BAD_REQUEST ||
        status === HttpStatus.UNAUTHORIZED ||
        status === HttpStatus.FORBIDDEN ||
        status === HttpStatus.INTERNAL_SERVER_ERROR // Return 403 if auth returns 500
      );
    }
    return false;
  }

  isBadRequestResponse(err: any): boolean {
    return err.response && err.response?.status === HttpStatus.BAD_REQUEST;
  }

  isHealthRequest(request: any): boolean {
    // return true if url matches /health
    return /^\/health$/.test(request.url);
  }

  isPublicDecorated(context: ExecutionContext): boolean {
    return this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
  }

  isPublic(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    return this.isHealthRequest(request) || this.isPublicDecorated(context);
  }

  attachAuthInfo(request: any, result: AuthorizationResponseDto) {
    request['username'] = result.username;
    request['userId'] = result.userId;
    request['authorities'] = result.authorities;

    this.attachTraceInfo(result);
  }

  attachTraceInfo(result: AuthorizationResponseDto) {
    setTagOnParentSpan(
      new Map<string, any>([
        ['request.authentication.userId', result.userId],
        ['request.authentication.username', result.username],
      ]),
    );
  }
}
