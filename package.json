{"name": "vidmob-acs-bff", "version": "0.0.0-SNAPSHOT", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "NODE_OPTIONS='--max-old-space-size=4096' node --expose-gc --no-compilation-cache ./node_modules/jest-cli/bin/jest.js --coverage --logHeapUsage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@anthropic-ai/sdk": "^0.40.1", "@automapper/classes": "8.8.1", "@automapper/core": "8.8.1", "@automapper/nestjs": "8.8.1", "@automapper/types": "6.3.1", "@aws-sdk/client-s3": "^3.662.0", "@aws-sdk/credential-providers": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.662.0", "@casl/ability": "6.5.0", "@elastic/elasticsearch": "8.12.1", "@google-cloud/vertexai": "^1.10.0", "@google/genai": "^0.15.0", "@google/generative-ai": "^0.24.1", "@nartc/automapper": "7.0.4", "@nestjs/axios": "^3.1.3", "@nestjs/common": "10.4.18", "@nestjs/config": "3.3.0", "@nestjs/core": "10.4.18", "@nestjs/platform-express": "10.4.18", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "8.1.1", "@nestjs/terminus": "10.0.1", "@nestjs/typeorm": "10.0.2", "@pinecone-database/pinecone": "^3.0.3", "@vidmob/vidmob-authorization-service-sdk": "0.0.0-SNAPSHOT-2b91220d-20241002200411", "@vidmob/vidmob-dashboard-service-sdk": "0.0.0-SNAPSHOT-2edd6aef-20250703011217", "@vidmob/vidmob-media-conversion-service-sdk": "0.0.0-SNAPSHOT-2ab42e08-20231003192528", "@vidmob/vidmob-nestjs-common": "2.0.1", "@vidmob/vidmob-organization-service-sdk": "0.0.0-SNAPSHOT-8e571278-20250620184045", "@vidmob/vidmob-soa-analytics-service-sdk": "0.0.0-SNAPSHOT-bd912fa6-20250630193527", "@vidmob/vidmob-soa-media-annotation-service-sdk": "0.0.0-SNAPSHOT-4294f437-20250218152502", "@vidmob/vidmob-soa-notification-service-sdk": "0.0.0-SNAPSHOT-1c4eba74-20240722155537", "@vidmob/vidmob-soa-scoring-service-sdk": "0.0.0-SNAPSHOT-cbeef8cf-20250627150546", "@vidmob/vidmob-studio-service-sdk": "0.0.0-SNAPSHOT-b6945933-20241212135224", "axios": "1.6.7", "class-transformer": "0.5.1", "class-validator": "0.14.0", "dayjs": "1.11.9", "fast-csv": "4.3.6", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "helmet": "7.1.0", "md5": "2.3.0", "mongodb": "^5.9.2", "mysql2": "3.10.3", "nestjs-ddtrace": "5.0.0", "openai": "4.61.1", "reflect-metadata": "0.1.13", "rxjs": "7.2.0", "typeorm": "0.3.17", "uuid": "9.0.0", "zod": "^3.23.8", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.18", "@types/express": "^4.17.17", "@types/jest": "29.5.12", "@types/js-yaml": "^4.0.5", "@types/md5": "^2.3.2", "@types/node": "18.15.11", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "29.5.0", "js-yaml": "^4.1.0", "prettier": "^2.8.8", "source-map-support": "^0.5.20", "supertest": "^6.3.3", "ts-jest": "29.2.3", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "4.2.0", "typescript": "^5.1.6"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}, "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}